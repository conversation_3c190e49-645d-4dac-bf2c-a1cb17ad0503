import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';

export function findIndexNew(type){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/information/api/findIndexNew?type=${type}&page=1&size=10&source=SMS`,
        contentType:'application/json;charset=UTF-8'
    })
}
export function indexNetWorkShow(type){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/networkList/api/indexNetWorkShow?type=${type}&source=SMS`,
        contentType:'application/json;charset=UTF-8'
    })
}
export function findSysFile(id){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/index/api/findSysFile?id=${id}&source=SMS`,
        contentType:'application/json;charset=UTF-8'
    })
}

// 问卷答题  
export function constructExamLayout(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examAttribute/api/constructExamLayout?source=SMS&currentUserCode=${store.state.user.user.username}`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}

export function findExamInfo(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/findExamInfo?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}

// 保存题号及答案
export function saveExam(data){    
    return request({
        // url:`${process.env.VUE_APP_URL}/action/examInfo/api/saveExam?source=SMS`,
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/saveExamSalt?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        // contentType: 'x-www-form-urlencoded',
        // data:data,
        data:{str: util.encryptAES(JSON.stringify(data))},
        catch:true
    })
}

// 提交
export function submitExam(data){    
    return request({
        // url:`${process.env.VUE_APP_URL}/action/examInfo/api/submitExam?source=SMS`,
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/submitExamSalt?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        // data:data
         data:{str: util.encryptAES(JSON.stringify(data))},
    })
}

// 获取分数
export function computeScore(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examAttribute/api/computeScore?source=SMS&userName=${store.state.user.user.username}&examCode=${data.examCode}&examAppCode=${data.examAppCode}`,
        contentType:'application/json;charset=UTF-8'
    })
}

// 获取考试限时总时长
export function findExamTime(data){
    return request({
        url:`${process.env.VUE_APP_URL}/action/examAttribute/api/findOne?source=SMS`,
        data:data,
        contentType:'application/json;charset=UTF-8',
        catch:true
    })
}


