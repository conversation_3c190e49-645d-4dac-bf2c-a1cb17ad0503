<template>
  <div class="maintext">
      <!-- <div class="djs" v-if="djsdiv">
        <van-icon name="clock"  color="#1989fa"/>
        <van-count-down :time="time" format="时间剩余：HH:mm:ss" @change="nowTime"/>
      </div> -->

      <div class="xc textDec" v-if="examAppCode=='XC_2023526'">
        <h3>中国移动巡视（巡察）整改和成果运用调研问卷</h3>
        <p>为落实国资委党委巡视办《关于采取协同联动方式开展巡视整改和成果运用课题调研的通知》要求，按照集团公司巡视工作办公室调研工作安排，请您协助填写巡视（巡察）整改和成果运用调研问卷。谢谢!</p>
      </div>
      <!-- <hr> -->
      <div class="cyzd textDec" v-if="examAppCode=='2023_CYZD'">
        <h3>河南公司全面从严治党专项问卷调查</h3>
        <p>根据学习贯彻习近平新时代中国特色社会主义思想主题教育调查研究需要，为更加全面了解公司在落实全面从严治党主体责任、深入推进反腐倡廉建设方面存在的困难问题和意见建议，现开展落实全面从严治党专项问卷调查。</p>
        <p>请您根据日常了解，实事求是作答，避免漏答。如选择“其他情况”，请填写具体内容。</p>
      </div>

      <div class="textDec" v-if="examAppCode=='2023nbxc_01_sj'">
        <p>以下问题是对您单位领导班子及其成员的巡察调查。请根据您了解的情况，实事求是作答，避免漏答。(各组可自行增加个性化题目控制在15题以内)</p>
      </div>
      <div class="textDec" v-if="examAppCode=='2023nbxc_02_sj'">
        <p>领导人员和领导班子集体民主评议表（共计60题）</p>
      </div>


    <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div class="question">{{(i+1+'、') + item.questionName }}</div>
        <!-- 单选题 -->
        <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-radio :name="its.answerCode"  icon-size="18px">{{its.answerCode +'、' +its.answerContent }}</van-radio>
        </div>
          <van-field  autosize class="custom-field" v-if="item.answerList[funZM(result[i])]?item.answerList[funZM(result[i])].identification == '1':false"
           rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
        </van-radio-group>
        <!-- 多选题 -->
        <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-checkbox shape="square" :name="its.answerCode" icon-size="18px">{{ its.answerCode +'、' +its.answerContent }}</van-checkbox>
        </div>
          <div v-for="it in result[i]" :key="it">
            <van-field  autosize class="custom-field" v-if="item.answerList[funZM(it)]?item.answerList[funZM(it)].identification == '1':false"
                rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
          </div>
        </van-checkbox-group>

       <!-- 简答题 -->
        <van-field v-if="item.questionType === 'shortAnswer'" autosize class="custom-field"
           v-model="result[i]"  rows="3" type="textarea" placeholder="请填写"></van-field>
    </div>

    <div style="margin: 16px;">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
</div>
</template>
<script>
import store from '@/store';
import { Dialog } from 'vant';
import { Notify } from 'vant';
import { constructExamLayout,saveExam,findExamInfo,submitExam} from "@/api/homes.js";
export default {
  name:'asksurvey',
  data() {
    return {
      // examAppCode:'2023_CYZD',
      // examAppCode:'XC_2023526',
      // examAppCode:'KFTP',
      examAppCode:this.$route.query.examAppCode?this.$route.query.examAppCode:'',
      examCode:this.$route.query.examCode?this.$route.query.examCode:'',
      singleQuestionList: [], //单选
      moreQuestionList:[],    //多选
      shortAnswerQuestionList:[], //简答
      questionAll:{}, //全部试题
      zdytext:[],//自定义文本
      result:[],//提交结果
      time: 10000,//时间
      examAnswer:[],
      examRecord:[],
      stime:0,
      id:'',
      IntX:'', 
    }
  },
  mounted() {
  },
  created(){
      this.getList();
  },
  activated(){
    this.gettime()
  },
  methods:{
       funZM(str){ //字母
            if(str){
                return JSON.stringify(str).charCodeAt(1) - 65 
            }
        },
      getList(){
          let data = {examAppCode:this.examAppCode}
          constructExamLayout(data).then(res=>{
              this.singleQuestionList = res.data.singleQuestionList  //单选
              this.moreQuestionList = res.data.moreQuestionList  //多选
              this.shortAnswerQuestionList = res.data.shortAnswerQuestionList  //简答
              this.questionAll = this.singleQuestionList.concat(this.moreQuestionList, this.shortAnswerQuestionList);

              this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);
          })
      },
      gettime(){ //获取考试信息
        let data = {examAppCode:this.examAppCode,examCode:this.examCode,publishUsername:store.state.user.user.username}
        findExamInfo(data).then(res=>{
            //  this.time = res.data.residueTime * 1000
            //  this.examRecord  = res.data.examRecord
            //  this.id = res.data.id
            //  this.result = res.data.examAnswer.split(',')
             if(!res.data.isFinishExam && !res.data.isMarkingExam){
                // this.IntX =  setInterval(this.saveTimefun,6000)
             }else{

                // this.djsdiv = false
                //   Dialog.alert({
                //     title: '温馨提示',
                //     message: '您已完成调查问卷，感谢您的参与！',
                //     theme: 'round-button',
                //     confirmButtonColor:'#1989fa',
                //     }).then(() => {
                //         // window.location.href = "about:blank"
                        this.$router.push({
                            name:'success',
                        })
                //     });
             }
        })
      },
      saveTimefun(){
        let data = {
            examAppCode:this.examAppCode,
            examCode:this.examCode,
            publishUsername:store.state.user.user.username,
            residueTime:this.stime,
            examAnswer:this.result.toString(),  //考试答案
            examRecord:this.examRecord,         //考试记录
            id:this.id
        }
        saveExam(data).then(res=>{
        })
      },

      nowTime(timeData){
        const stime = parseInt(timeData.hours*60*60 +  timeData.minutes*60 + timeData.seconds)
        console.log(stime);
        if (stime !== 0 && stime % 4 === 0) {
            console.log('保存一次');
        } else {
          if(stime=='1'){
            console.log('倒计时结束');
          }
        }


      },
      submit(){
        let arr = [] 
        arr = this.result.filter(item => item && item !== '' );
        if(arr.length !== this.questionAll.length){
            return  Notify({ type: 'warning', message: '您有未完成的题目，请继续填写！' });
        }

        for(var i in this.result){
            if (typeof this.result[i] == 'string') {
                if (this.zdytext[i]) {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        this.result[i] = this.result[i] + ':' + this.zdytext[i]
                    }
                } else {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                    }
                }
            }else if(typeof this.result[i] == 'object') {
                if (this.zdytext[i]) {
                    let index =  this.result[i].length-1
                    this.result[i][index] = this.result[i][index] + ':' + this.zdytext[i]
                } else {
                    for(var v in this.result[i]){
                    if (this.questionAll[i].answerList[this.funZM(this.result[i][v])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                     }
                    }
                }
            }
        }

        for(var j in this.result){
            if(typeof this.result[j] !== 'string'){
                this.result[j] = this.result[j].join('/')
            }
        }

        let data = {
            examAppCode:this.examAppCode,
            examCode:this.examCode,
            publishUsername:store.state.user.user.username,
            residueTime:this.stime,
            examAnswer:this.result.toString(),
            examRecord:this.examRecord,
        }
        Dialog.confirm({
        title: '温馨提示',
        message: '您已完成了所有题目，请确认是否进行提交',
        confirmButtonColor:'#1989fa'
        }).then(() => {
            submitExam(data).then(res=>{
            if(res.status==200){
                clearInterval(this.IntX)
                this.result = []
                this.zdytext = []
                this.djsdiv = false
                this.$router.push({ name:'success', })
            }
        }) }).catch(() => {});
    },
  }
}
</script>

<style>
.maintext{ margin-left: .3rem; margin-right: .3rem; }
.van-radio{ margin-bottom: 0.05rem; align-items:flex-start }
.subject{ margin-bottom: 0.2rem; }
.djs{ position: fixed; top: 0.8rem; right: 0.3rem; background: #fff; display: flex; align-items: center; z-index: 2; }
.question{ line-height: 0.22rem; margin-bottom: 0.1rem; font-weight: 700; }
.custom-field { border: 1px solid #e8e8e8; border-radius: 5px; padding: 5px; }
.van-checkbox{ align-items: flex-start; margin-bottom: 3px; }

.textDec{ color: #333; font-size: 14px; line-height: 22px; margin-bottom: 3px; }
.textDec h3{ text-align: center; font-weight: bold; margin-bottom: 1px; }
.textDec p{ text-indent: 2em; }

</style>
