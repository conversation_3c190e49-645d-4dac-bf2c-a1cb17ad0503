import {password,cardNo,telphone,phone,phoneOrTel,zinteger,zintegerFrom1,znumber,znumberFrom1,dateD,ipv4,million,money,score,equals,equalLength,management,dateDays} from '@/assets/js/validate.js';
//exports.install=function(Vue,options){
export default {
install(Vue,options){
    //密码
    const isPwd=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=password(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //身份证号
    const iscardNo=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=cardNo(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //固定电话
    const istelphone=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=telphone(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //手机号
    const isphone=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=phone(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }    
    //固定电话或手机号
    const isphoneOrTel=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=phoneOrTel(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正整数(含0)
    const iszinteger=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=zinteger(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正整数(不含0)
    const iszintegerFrom1=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=zintegerFrom1(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正数(含0)
    const isznumber=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=znumber(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正数(不含0)
    const isznumberFrom1=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=znumberFrom1(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正数 1-1000
    const isznumber1000=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=znumber(value,1000);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正数 0-5000
    const isznumber5000=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=znumber(value,5000);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //日期
    const isdateD=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=date(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //IP
    const isipv4=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=ipv4(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //金额
    const ismillion=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=million(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //金额
    const ismoney=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=money(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //正数且最多保留两位小数
    const isscore=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=score(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //输入是否一致
    const isequals=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=equals(value,rule.extension);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //输入多个字符
    const isequalLength=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=equalLength(value,rule.extension);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback()
            }
        }else{
            callback();
        }
    }
    //大于等于0小于100的整数
    const ismanagement=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=management(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback();
            }
        }else{
            callback();
        }
    }
    //大于等于1小于等于31的整数
    const isdateDays=(rule,value,callback) => {
        if(value!=null && value !=''){
            let pwdMes=dateDays(value);
            if(typeof pwdMes=='string'){
                callback(new Error(pwdMes));
            }else{
                callback();
            }
        }else{
            callback();
        }
    }

    Vue.prototype.getformrules=function(item){
        let ii;
        let rules={};
        for(ii in item){            
            let ri=[];
            if(item[ii].required){
                ri.push({required:true,message:'该输入项为必填项',trigger:item.trigger?item.trigger:['blur','change']});
            }
            if(item[ii].maxLength){
                ri.push({min:1,max:item[ii].maxLength,message:'最多输入'+item[ii].maxLength+'个字符',trigger:item.trigger?item.trigger:['blur','change']});
            }
            if(item[ii].minLength){
                ri.push({min:item[ii].minLength,max:2000,message:'至少输入'+item[ii].minLength+'个字符',trigger:item.trigger?item.trigger:['blur','change']});
            }
            if(item[ii].min && item[ii].max){
                ri.push({ min:item[ii].min,max:item[ii].max, message:'字符长度在'+item[ii].min+'至'+item[ii].max+'之间!', trigger: item.trigger?item.trigger:['blur','change'] });
            }
            if(item[ii].type){
                let types=item[ii].type.split(",");
                let ti;
                for(ti in types){
                    switch(types[ti]) {
                        case 'email':
                            ri.push({ type: 'email', message: '请输入正确的邮箱地址', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'hex':
                            ri.push({ type: 'hex', message: '请输入正确的二进制数据', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'boolean':
                            ri.push({ type: 'boolean', message: '请输入正确的布尔值', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'number':
                            ri.push({ type: 'number', message: '请输入正确的数字', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'string':
                            ri.push({ type: 'string', message: '请输入正确的字符串', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'url':
                            ri.push({ type: 'url', message: '请输入正确的 url 地址', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'date':
                            ri.push({ type: 'date', message: '请输入正确的日期', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'enum':
                            ri.push({ type: 'enum',enum:item[ii].enum, message: '请输入或选择列表内的数据', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'object':
                            ri.push({ type: 'object',fields:item[ii].fields, message: '请输入正确的数据', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'array':
                            ri.push({ type: 'array',array:item[ii].array, message: '请输入或选择数组内的数据', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'float':
                            ri.push({ type: 'float', message: '请输入正确的小数', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'integer':
                            ri.push({ type: 'integer', message: '请输入正确的整数', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'regexp':
                            ri.push({ type: 'regexp',pattern:item[ii].pattern, message: '请输入正确的正则表达式', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'method':
                            ri.push({ type: 'method', message: '请输入正确的函数', trigger: item.trigger?item.trigger:['blur','change']  });
                            break;
                        case 'password'://密码
                            ri.push( { validator: isPwd, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;
                        case 'cardNo'://身份证号
                            ri.push( { validator: iscardNo, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'telphone'://固定电话
                            ri.push( { validator: istelphone, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'phone'://手机号
                            ri.push( { validator: isphone, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;   
                        case 'phoneOrTel'://固定电话或手机号
                            ri.push( { validator: isphoneOrTel, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;
                        case 'zinteger'://正整数
                            ri.push( { validator: iszinteger, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'zintegerFrom1'://正整数(不含0)
                            ri.push( { validator: iszintegerFrom1, trigger: item.trigger?item.trigger:['blur','change'] });
                            break; 
                        case 'znumber'://正数
                            ri.push( { validator: isznumber, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'znumberFrom1'://正数(不含0)
                            ri.push( { validator: isznumberFrom1, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'znumber1000'://正数 1-1000
                            ri.push( { validator: isznumber1000, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'znumber5000'://正数 0-5000
                            ri.push( { validator: isznumber5000, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'dateD'://日期
                            ri.push( { validator: isdateD, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'ipv4'://IP
                            ri.push( { validator: isipv4, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;
                        case 'million'://金额
                            ri.push( { validator: ismillion, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        case 'money'://金额
                            ri.push( { validator: ismoney, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;   
                        case 'score'://正数，最多保留两位小数
                            ri.push( { validator: isscore, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;   
                        case 'equals'://输入是否一致
                            ri.push( { validator: isequals, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;     
                        case 'equalLength'://输入多个字符
                            ri.push( { validator: isequalLength, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;     
                        case 'management': //大于等于0小于100的整数
                        	ri.push({validator: ismanagement, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;
                        case 'dateDays': //大于等于0小于100的整数
                        	ri.push({validator: isdateDays, trigger: item.trigger?item.trigger:['blur','change'] });
                            break;    
                        default:
                            ri.push({});
                            break;
                    }
                }
            }
            rules[ii]=ri;
        }
        return rules;
    };
}
};