div {
    font-size: 0.14rem;
}
html,body,#app{width:100%;height:100%;}
div.van-toast__text{font-size:0.14rem;}
/* 字体 */
.font12 {font-size: 0.12rem;}
.font13 {font-size: 0.13rem;}
.font14 {font-size: 0.14rem;}
.font15 {font-size: 0.15rem;}
.font16 {font-size: 0.16rem;}
.font17 {font-size: 0.17rem;}
.font18 {font-size: 0.18rem;}
.font20 {font-size: 0.2rem;}
.font22 {font-size: 0.22rem;}
.font24 {font-size: 0.24rem;}
.font26 {font-size: 0.26rem;}
.font28 {font-size: 0.28rem;}
.text-c {text-align: center;}
.text-r {text-align: right;}
.text-l {text-align: left;}
/* 颜色 */
/* 主题颜色 */
.bg-theme {background: #7ABCF8;}
.color-theme {color: #00A0E9;}
.color-f {color: #fff;}
.color-a {color: #aaa;}
.color-b {color: #bbb;}
.color-c {color: #ccc;}
.color-2 {color: #222;}
.color-3 {color: #333;}
.color-6 {color: #666;}
.color-8 {color: #888;}
.color-8b {color: #8b8b8b;}
.color-e5 {color: #E5E5E5;}
.color-9 {color: #999;}
.color-red {color: #ff0000;}
.color-blue {color:#00A0E9;}
.color-qblue {color:#7ABCF8;}
.bcolor-qblue {background-color:#7ABCF8;}
.bcolor-f{background-color: #fff;}
.bcolor-e{background-color: #eee;}
.broder-e{border: solid 1px #eee;}
.backc-f7{background-color:#F7F7F7;}
/* 定位 */
.pos-r {position: relative;}
.pos-a {position: absolute;}
.pos-f {position: fixed;}
.fr {float: right;}
.fl {float: left;}
/* 透明度 */
.op6 {opacity: 0.6;}
.op8 {opacity: 0.8;}
/* 自定义样式 */
.display-b {display: block;}
.display-ib {display: inline-block;}
.vertical-a-t {vertical-align: top;}
.mt10 {margin-top:0.10rem;}
.mt5{margin-top: 0.05rem;}
.pb5{padding-bottom: 0.05rem;}
.mr5 {margin-right: 0.05rem;}
.mr7{margin-right: 0.07rem;}
.mr1{margin-right: 0.01rem;}
.overflow-h {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.overflow-hTwo{
    display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;
    overflow: hidden;
}
.pl08 {padding: 0 0.10rem 0 0.08rem;}
.p109 {padding: 0.10rem 0.09rem;}
.p1012 {padding: 0.10rem 0.12rem;}
.mlr-10{padding: 0 0.10rem}
.ml1{margin-left: 0.1rem;}
.iback,.pos-a-l0 {
    position: absolute;
    left: 0;
}
.pos-a-l9{
    position: absolute;
    left: 0.09rem;
}
.pos-a-r9{
    position: absolute;
    right: 0.09rem;
}
.isea,.pos-a-r0 {
    position: absolute;
    right: 0;
}
.ht100 {
    height: 100%;
    overflow-y: auto;
}
.pd05 {
    padding-bottom: 0.50rem;
}
.van-field--disabled .van-field__label {
    color: #646566;
}
.van-field__control:read-only {
    color: #c8c9cc;
}

.van-field__label,
.van-field__value,
.van-field__body,
.van-cell__title, 
.van-cell__value,
.van-ellipsis,
.van-calendar__selected-day,
.van-dialog__header,
.van-button__content {
    font-size: unset;
}
.titleBefore{display: inline-block;height:0.15rem;width: 0.03rem;background-color: #00A0E9;margin-right: 0.10rem;position: relative;top:0.02rem;}
.cutLine{width:100%;height: 0.07rem;background-color: #F7F7F7;margin-top: 0.15rem;}
.lineh{line-height:0.22rem;}
.iconcurrent{color:#A3CF1F;}
.iconyiban{color:#00A0E9;}
.icondelete{color:#ff0000;}
.noData{text-align: center;width: 100%;margin-top: 1rem;color: #ccc;}