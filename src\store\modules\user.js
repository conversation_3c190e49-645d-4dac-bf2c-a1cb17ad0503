import { login, logout, getInfo, getMenus,oauthToken } from "@/api/login";
import { getToken, setToken, removeToken } from "@/assets/js/auth";
// import avatar_default1 from "@/assets/images/gender1.png";
// import avatar_default2 from "@/assets/images/gender2.png";
// import tab from "./tab";
import router from "@/router";
const user = {
	state: {
		city:'',
		token: getToken(),
		user: null,
		roles: [],
		menus: []
	},
	mutations: {
		SET_CITY: (state, city) => {
			state.city = city;
		},
		SET_TOKEN: (state, token) => {
			state.token = token;
			setToken(token);
		},
		SET_USER: (state, user) => {
			state.user = user;
		},
		SET_ROLES: (state, roles) => {
			state.roles = roles;
		},
		SET_MENUS: (state, menus) => {
			state.menus = menus;
		}
	},
	actions: {
		changeCity({ commit },city) {
			commit("SET_CITY",city)
		},
		//设置token
		SetToken({ commit }) {
			return new Promise((resolve,reject)=>{
				let params={grant_type:'client_credentials',scope:'all',client_id:'exam_sms',client_secret:process.env.VUE_APP_SECRET,appcode:'exam'};
				oauthToken(params).then(response=>{	
					console.log("SetToken");
					commit("SET_TOKEN", response.access_token);
					resolve(response);
				}).catch(error=>{
					reject(error);
				});
			});
		},
		//登录
		Login({ commit }, userInfo) {
			// console.log(userInfo)
			const phone = userInfo.phone?.trim();
			const smscode = userInfo.smscode?.trim();
			// console.log(phone)
			return new Promise((resolve, reject) => {
				let params={phone,grant_type:'phone',scope:'all',client_id:'exam_sms',client_secret:process.env.VUE_APP_SECRET,appcode:'exam'};
				if(smscode){
					params.smscode=smscode;
					params.grant_type="phonecode";
				}
				if(phone){
					login(params).then(response => {
						console.log("Login");
						const data = response;
						const tokenStr = data.access_token;
						commit("SET_TOKEN", tokenStr);
						resolve(response);
					})
					.catch(error => {
						reject(error);
					});
				}else{
					reject({message:'手机号必传！'});
				}
			});
		},
		//获取用户信息
		GetInfo({ commit, state }) {
			return new Promise((resolve, reject) => {
				getInfo()
					.then(response => {
						if(response.data){
							let data = response.data;
							// if (data.authRoles && data.authRoles.length > 0) {
							// 	commit("SET_ROLES", data);
							// } else {
							// 	reject("getinfo: roles must be a non-null array !");
							// }
							// commit('SET_NAME',data.username);
							//commit('SET_AVATAR',data.icon || avatar_default);
							// data.preferredMobileI=data.preferredMobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
							commit("SET_USER", data);
							resolve(response);
						}else{
							window.location.href = "http://hfx.net/i/B9KTr0";
						}
					})
					.catch(error => {
						reject(error);
					});
			});
		},
		//登出
		LogOut({ commit, state }) {
			return new Promise((resolve, reject) => {
				logout(state.token)
					.then(response => {
						//commit('SET_AVATAR',data.icon);
						commit("SET_USER", {});
						commit("SET_MENUS", []);
						if (router.options.routes.length > 3)
							router.options.routes.splice(3);
						resolve(response);
					})
					.catch(error => {
						reject(error);
					});
			});
		},
		//前端 登出
		FedLogOut({ commit }) {
			return new Promise(resolve => {
				commit("SET_TOKEN", "");
				commit("SET_MENUS", []);
				removeToken();
				resolve();
			});
		},
		//获取菜单
		GetMenus({ commit, state }) {
			return new Promise((resolve, reject) => {			
				getMenus(state.user.username)
				.then(res => {
					commit("SET_MENUS", res.data);
					resolve(res);
				})
				.catch(error => {
					reject(error);
				});
			});
		}
	}
};
export default user;
