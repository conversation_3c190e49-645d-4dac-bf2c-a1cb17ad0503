<template>
  <div class="maintext">
      <div class="cyzd textDec">
        <div>亲爱的会员</div>
        <div>您好！</div>
        <p>为深入学习贯彻党的二十大会议精神和工会十八大精神，落实党委安排部署，持续加强民主管理、技能竞赛和员工关爱等工作，不断提升工会工作水平，为员工反映需求心声、提出意见建议搭建平台，加强与广大员工的联系与沟通，为高水平创建世界一流企业而团结奋斗，现开展2023年度工会会员问卷调查。</p>
        <p>本评价对内保密，预计占用您五分钟时间。您的真实、公正打分将有助于我们为您提供更加优质、完善的服务和直接、有效的工作支持！</p>
      </div>
      
      <div  style="padding:5px 0">
        <!-- <span  style="font-weight:700">姓名：{{truename}}</span> -->
        <div style="display:flex;padding: 5px 0 0;font-weight:700">
          <span>性别：</span>
          <van-radio-group v-model="sex" direction="horizontal">
            <van-radio icon-size="18px" name="男">男</van-radio>
            <van-radio icon-size="18px" name="女">女</van-radio>
          </van-radio-group>
        </div>
      </div>

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div class="question">{{(i+1+'、') + item.questionName }}</div>
        <!-- 单选题 -->
        <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-radio :name="its.answerCode"  icon-size="18px">{{its.answerCode +'、' +its.answerContent }}</van-radio>
        </div>
          <van-field  autosize class="custom-field" v-if="item.answerList[funZM(result[i])]?item.answerList[funZM(result[i])].identification == '1':false"
           rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
        </van-radio-group>
        <!-- 多选题 -->
        <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-checkbox shape="square" :name="its.answerCode" icon-size="18px">{{ its.answerCode +'、' +its.answerContent }}</van-checkbox>
        </div>
          <div v-for="it in result[i]" :key="it">
            <van-field  autosize class="custom-field" v-if="item.answerList[funZM(it)]?item.answerList[funZM(it)].identification == '1':false"
                rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
          </div>
        </van-checkbox-group>

       <!-- 简答题 -->
        <van-field v-if="item.questionType === 'shortAnswer'" autosize class="custom-field"
           v-model="result[i]"  rows="3" type="textarea" placeholder="请填写"></van-field>
    </div>

    <div style="margin: 16px;">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>

    <van-popup v-model="giftShow">
      <div class="giftBox">
        <img  style="width:100%" src="@/assets/images/jpeg.gif" alt=""/>
      </div>
    </van-popup>

   
</div>
</template>
<script>
import store from '@/store';
import { Dialog } from 'vant';
import { Notify } from 'vant';
import { constructExamLayout,saveExam,findExamInfo,submitExam,findEffectiveExamByExamCode,isLottery,createToJackpotReduce} from "@/api/homes.js";
import datas from "@/api/data.js";

export default {
  name:'asksurvey',
  data() {
    return {
      examAppCode:this.$route.query.examAppCode?this.$route.query.examAppCode:'',
      examCode:this.$route.query.examCode?this.$route.query.examCode:'',
      singleQuestionList: [], //单选
      moreQuestionList:[],    //多选
      shortAnswerQuestionList:[], //简答
      questionAll:{}, //全部试题
      zdytext:[],//自定义文本
      result:[],//提交结果
      time: 10000,//时间
      examAnswer:[],
      examRecord:[],
      stime:0,
      id:'',
      IntX:'', 
      datas:datas,
      truename:store.state.user.user.truename,//姓名
      sex:'',//性别
      giftShow:false
    }
  },
  mounted() {
  },
  created(){
      // this.getList();
  },
  activated(){
    this.gettime()
  },
  methods:{
       funZM(str){ //字母转换
            if(str){
                return JSON.stringify(str).charCodeAt(1) - 65 
            }
        },
      getList(){
          let data = {examAppCode:this.examAppCode}
          constructExamLayout(data).then(res=>{
              this.singleQuestionList = res.data.singleQuestionList  //单选
              this.moreQuestionList = res.data.moreQuestionList  //多选
              this.shortAnswerQuestionList = res.data.shortAnswerQuestionList  //简答
              this.questionAll = this.singleQuestionList.concat(this.moreQuestionList, this.shortAnswerQuestionList);
              this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);
              // this.questionAll = this.datas.data.singleQuestionList.concat(this.datas.data.moreQuestionList, this.datas.data.shortAnswerQuestionList);
              // console.log(this.questionAll);

              let Record =''
              for (var i = 0; i < this.questionAll.length; i++) {
                  Record += this.questionAll[i].questionCode + ',';
              }
              this.examRecord = Record.substring(0, Record.length - 1);

              // console.log(this.examRecord);

          })
      },
      gettime(){ //获取考试信息
        // 判断是否可以开始答题
        findEffectiveExamByExamCode({examCodes: this.examCode}).then((res) => {
				if(res.data.showFlag === true){
          let data = {examAppCode:this.examAppCode,examCode:this.examCode,publishUsername:store.state.user.user.username}
          findExamInfo(data).then(res=>{
            if(res.data){
              if(!res.data.isFinishExam && !res.data.isMarkingExam){
                this.getList();
              }else{
                this.$router.push({ name:'success', })
              }
            }else{
              this.getList();
            }
          })
				}else{
					Dialog.alert({
						title: "",
						message: "竞赛不在考试时间范围或者暂未权限！",
					}).then(() => {
					      window.close()
                window.open('about:blank', '_self');
					});
				}
			}).catch(() => {
				Dialog.alert({
					title: "",
					message: "竞赛不在考试时间范围或者暂未权限！",
				}).then(() => {
          window.close()
          window.open('about:blank', '_self');
				});
			});
      },
      // saveTimefun(){
      //   let data = {
      //       examAppCode:this.examAppCode,
      //       examCode:this.examCode,
      //       publishUsername:store.state.user.user.username,
      //       residueTime:this.stime,
      //       examAnswer:this.result.toString(),  //考试答案
      //       examRecord:this.examRecord,         //考试记录
      //       id:this.id
      //   }
      //   saveExam(data).then(res=>{
      //   })
      // },

      // nowTime(timeData){
      //   const stime = parseInt(timeData.hours*60*60 +  timeData.minutes*60 + timeData.seconds)
      //   console.log(stime);
      //   if (stime !== 0 && stime % 4 === 0) {
      //       console.log('保存一次');
      //   } else {
      //     if(stime=='1'){
      //       console.log('倒计时结束');
      //     }
      //   }
      // },
      submit(){
        if(!this.sex){
          return  Notify({ type: 'warning', message: '请在最上方选择您的性别！' });
        }

        let arr = [] 
        arr = this.result.filter(item => item && item !== '' );
        if(arr.length !== this.questionAll.length){
            return  Notify({ type: 'warning', message: '您有未完成的题目，请继续填写！' });
        }

        for(var i in this.result){
            if (typeof this.result[i] == 'string') {
                if (this.zdytext[i]) {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        this.result[i] = this.result[i] + ':' + this.zdytext[i]
                    }
                } else {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                    }
                }
            }else if(typeof this.result[i] == 'object') {
                if (this.zdytext[i]) {
                    let index =  this.result[i].length-1
                    this.result[i][index] = this.result[i][index] + ':' + this.zdytext[i]
                } else {
                    for(var v in this.result[i]){
                    if (this.questionAll[i].answerList[this.funZM(this.result[i][v])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                     }
                    }
                }
            }
        }

        for(var j in this.result){
            if(typeof this.result[j] !== 'string'){
                this.result[j] = this.result[j].join('/')
            }
        }

        let data = {
            examAppCode:this.examAppCode,
            examCode:this.examCode,
            publishUsername:store.state.user.user.username,
            residueTime:this.stime,
            examAnswer:this.result.toString(),
            examRecord:this.examRecord,
            sex:this.sex
        }
        // console.log(data);
        Dialog.confirm({
        title: '温馨提示',
        message: '您已完成了所有题目，请确认是否进行提交',
        confirmButtonColor:'#1989fa'
        }).then(() => {
            submitExam(data).then(res=>{
            if(res.status==200){
                clearInterval(this.IntX)
                this.result = []
                this.zdytext = []
                this.djsdiv = false
                // this.$router.push({ name:'success', })

                Dialog.confirm({
                title: '温馨提示',
                message: '您已提交，请确认是否进行抽奖',
                confirmButtonColor:'#1989fa'
                }).then(() => {
                  this.giftShow = true
                  let that = this
                  setTimeout(function(){
                    isLottery({examCodes:data.examCode}).then(res=>{
                      that.giftShow = false
                    if(res.data===true){
                        Notify({ type: 'warning', message: '恭喜您中奖了！请您填写个人信息' });
                        that.$router.push({ name:'info', query: that.$route.query})
                    }
                    if(res.data===false){
                       Notify({ type: 'warning', message: '很遗憾，没有中奖' });
                       that.$router.push({ name:'success', })
                    }
                  }).catch(()=>{})
                  },2000)
                }).catch(() => {
                   this.$router.push({ name:'success', })
                });
            }
        }) }).catch(() => {});
    },

      // 返回
		  goBack(){
			  this.$router.push({path: "/detail"});
		  },
  }
}
</script>

<style>
.maintext{ margin-left: .3rem; margin-right: .3rem; }
.van-radio{ margin-bottom: 0.05rem; align-items:flex-start }
.subject{ margin-bottom: 0.2rem; }
.djs{ position: fixed; top: 0.8rem; right: 0.3rem; background: #fff; display: flex; align-items: center; z-index: 2; }
.question{ line-height: 0.22rem; margin-bottom: 0.1rem; font-weight: 700; }
.custom-field { border: 1px solid #e8e8e8; border-radius: 5px; padding: 5px; }
.van-checkbox{ align-items: flex-start; margin-bottom: 3px; }

.textDec{ color: #333; font-size: 14px; line-height: 22px; margin-bottom: 3px; }
.textDec h3{ text-align: center; font-weight: bold; margin-bottom: 1px; }
.textDec p{ text-indent: 2em; }

/* .goBack{
	  position: absolute;
	  right: 0rem;
	  top: 1rem;
	  width: .22rem;
  } */

  .giftBox{
    text-align: center;
    /* position: absolute;
    top: 40%; */
  }

</style>
