<!-- 首页 -->
<template>
    <div class="content">
      <div class="tit">
        <span style="display: block;">{{user.truename}}您好，欢迎参加</span>
        <span style="display: block;">河南移动“赋能建功”2024年合规知识竞赛</span>
      </div>
      <div class="card" @click="mrtest">
        <div class="type" >每日答题</div>
        <div><img style="width:35%" src="@/assets/images/zsjs/1.png" alt="" ></div>
      </div>
      <div class="card" @click="tztest" style="margin:20px auto">
        <div class="type">挑战答题</div>
        <div><img style="width:35%" src="@/assets/images/zsjs/2.png" alt="" ></div>
      </div>
      <div class="card" @click="rrtest">
        <div class="type" >人人对战</div>
        <div><img style="width:35%" src="@/assets/images/zsjs/3.png" alt="" ></div>
        <div class="type" v-if="pendingNum > 0" style="font-size: 0.15rem;">您有{{pendingNum}}个待处理的对战</div>
      </div>

      <!-- <div style="margin:20px 0 0px;color:#fff"><span @click="toRank">点击查看当前排名 {{user.truename}}</span></div> -->
      <div style="margin:20px 0 0px;color:#fff"><span @click="toRank">点击查看当前排名</span></div>
      <div style="margin:0 0 10px;color:#fff"><span >全省排名：{{self.rank == '9999999' ? '无' : self.rank}}, 累计得分：{{self.totalScore}}, 用时{{ self.minute}}分{{self.second}}秒</span></div>
      <!--  -->
    </div>
</template>
<script>
import store from '@/store';
import { usDailyQuestions_answersRecordCheck,usChallengeQuestions_answersRecordCheck,countExamWorkByTask,getSocreRanking,pendingList } from "@/api/test.js";
import { Dialog } from 'vant';
export default {
  name:'home',
  data() {
    return {
      user:store.state.user.user,//当前登陆人信息
      workType:'',
      self:{
        rank: '',
        minute: '',
        second: '',
        totalScore: ''
      },
      pendingNum: 0
    }
  },
  mounted() {},
  created(){
    this.getinfo()
    this.getPendingNum()
  },
  activated(){ },
  methods:{
    getPendingNum() {
      pendingList().then(res=>{
        this.pendingNum = res.data.length
      })
    },
     getinfo(){
      getSocreRanking().then(res=>{
          this.self = {
            rank: res.data.self.rank,
            minute: res.data.self.timeSpentMinutes.indexOf('.') != -1 ? res.data.self.timeSpentMinutes.split('.')[0] : res.data.self.timeSpentMinutes,
            second: res.data.self.timeSpentMinutes.indexOf('.') != -1 ? res.data.self.timeSpentMinutes.split('.')[1] : 0,
            totalScore: res.data.self.totalScore
          }
        })
      },
      // 查看当前排名
      toRank(){
         this.$router.push({ name:'rank'})
      },
      // 每日答题
      mrtest(){
        this.workType = 'A'
        usDailyQuestions_answersRecordCheck().then(res=>{
          if(res.data.ifCountinue){
                this.$router.push({ name:'test', params:{answerRecordId:res.data.answerRecordId,workType:this.workType} })
          }else{
            if(res.data.toAnswer == '0'){
              Dialog.alert({ title: '温馨提示', message: '您今天已答题，请明天再答!', }).then(() => {});
            }else{
                Dialog.confirm({ title: '温馨提示', message: `您今天已答题${res.data.answerd}次，剩余答题${res.data.toAnswer}次!` }).then(() => {
                  this.$router.push({ name:'test', params:{answerRecordId:res.data.answerRecordId,workType:this.workType}})
                }).catch(() => {});
            }
          }
        })
      },
      // 挑战答题
      tztest(){
        this.workType = 'B'
        usChallengeQuestions_answersRecordCheck().then(res=>{
            if(res.data.toAnswer == '0'){
              Dialog.alert({ title: '温馨提示', message: '您今天已完成挑战答题，请明天再进行挑战!', }).then(() => {});
            }else{
                Dialog.confirm({ title: '温馨提示', message: `每天可以挑战3次，您已挑战${res.data.answerd}次剩余${res.data.toAnswer}次机会，注意每道题答题时间为30s、答错后显示正确答案，关闭或退出答题页面即为本次挑战结束！` }).then(() => {
                  this.$router.push({ name:'test', params:{answerRecordId:res.data.answerRecordId,workType:this.workType}})
                }).catch(() => {});
            }
        })
      },
      // 人人对战
      rrtest(){
        // this.workType = 'C'
        // countExamWorkByTask({recUserName:''}).then(res=>{
        //   Dialog.confirm({ title: '温馨提示', message: `人人对战每人每天可参与${res.data.everyoneAnswerCount}次，剩余${res.data.unAnswerdCount}次!` }).then(() => {
        //       if(res.data.answerdCount == res.data.everyoneAnswerCount){
        //           Dialog.alert({ title: '温馨提示', message: '您今日人人对战已完成，请明日继续参与。', }).then(() => {});
        //       }else{
                  this.$router.push({ name:'select', params:{workType:this.workType}})
        //       }
        //   }).catch(() => {});
        // })
      },
  }
}

</script>

<style scoped>
.content{
	width: 100vw;
	min-height: 100vh;
  text-align: center;
  background-image: linear-gradient(to bottom, #326be1, #2cb2ed);
  overflow: hidden;
}
.content .tit{
  color: #fff;
  font-weight: 700;
  font-size: .18rem;
  padding: .4rem 0  .4rem;
}
.card{
  width: 80%;
  min-height: 20vh;
  margin: 0 auto;
  background: #fff;
  border-radius: 10px;
 
}
.card .type{
  color: #333;
  font-weight: 700;
  font-size: .2rem;
  padding: .1rem 0;
}
.flexsb{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>