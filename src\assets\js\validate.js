//密码
export function password(val){
    const regex=/^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])[a-zA-Z0-9!@#$%^&*,.\w]{8,}$/;
    let message="不能小于8位,至少包含大小写字母和数字";
    return regex.test(val)?true:message
}
//身份证号
export function cardNo(val){
    const regex=/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
    let message="身份证输入不合法";
    return regex.test(val)?true:message
}
//固定电话
export function telphone(val){
    const regex=/^((0[0-9]{2,3}-)?([2-9][0-9]{6,7})+(-[0-9]{1,4})?)$/;
    let message="无效的固话号码";
    return regex.test(val)?true:message
}
//手机号
export function phone(val){
    const regex=/^(1)\d{10}$/;///^([\+][0-9]{1,3}[ \.\-])?([\(]{1}[0-9]{2,6}[\)])?([0-9 \.\-\/]{7,27})((x|ext|extension)[ ]?[0-9]{1,4})?$/
    let message="无效的手机号";
    return regex.test(val)?true:message
}
//固定电话或手机号
export function phoneOrTel(val){
    const regex=/^(((0[0-9]{2,3}-)?([2-9][0-9]{6,7})+(-[0-9]{1,4})?)|((1)\d{10}))$/;
    let message="无效的手机号或固话号码";
    return regex.test(val)?true:message
}
//正整数(含0)
export function zinteger(val){
    const regex=/^\d+$/;
    let message="不是有效的正整数";
    return regex.test(val)?true:message
}
//正整数(不含0)
export function zintegerFrom1(val){
    // const regex=/^[1-9]\d+$/;
    const regex=/^[1-9]\d*$/;
    let message="不是有效的正整数";
    return regex.test(val)?true:message
}
//正数(含0)
export function znumber(val,maxVal){
    const regex=/^(([0-9]+)([\.,]([0-9]+))?|([\.,]([0-9]+))?)$/;
    let message="无效的数字";//正数
    if(maxVal){
        if(regex.test(val)){
            if(val<=maxVal){
                return true;
            }else{
                return '输入上限为'+maxVal;
            }
        }
    }
    return regex.test(val)?true:message
}
//正数(不含0)
export function znumberFrom1(val,maxVal){
    const regex=/^([1-9]\d*)(\.{0,1}\d*[1-9])?$/;
    let message="无效的数字";//正数
    if(maxVal){
        if(regex.test(val)){
            if(val<=maxVal){
                return true;
            }else{
                return '输入上限为'+maxVal;
            }
        }
    }
    return regex.test(val)?true:message
}
//日期
export function dateD(val){
    const regex=/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/;
    let message="无效的日期,格式必需为 年-月-日";
    return regex.test(val)?true:message
}
//IP
export function ipv4(val){
    const regex=/^((([01]?[0-9]{1,2})|(2[0-4][0-9])|(25[0-5]))[.]){3}(([0-1]?[0-9]{1,2})|(2[0-4][0-9])|(25[0-5]))$/;
    let message="无效的 IP 地址";
    return regex.test(val)?true:message
}
//金额
export function million(val){
    const regex=/^\d{1,10}(?:\.\d{1,6})?$/;
    let message="金额输入错误,以万元为单位保留6位小数点";
    return regex.test(val)?true:message
}
//金额
export function money(val){
    const regex=/^\d{1,10}(?:\.\d{1,2})?$/;
    let message="金额输入错误,以元为单位保留2位小数点";
    return regex.test(val)?true:message
}
//正数且最多保留两位小数
export function score(val){
    const regex=/^\d{1,10}(?:\.\d{1,2})?$/;
    let message="请输入正数,最多保留2位小数";
    return regex.test(val)?true:message
}
//输入是否一致
export function equals(val,cval){
    let message="输入不一致";
    return val===cval?true:message
}
//输入多个字符
export function equalLength(val,cval){
    let len;
    let result=false;
    for(len in cval){
        if(len===val.length) result=true;
    }
    let message="请输入 "+cval.join("或")+" 个字符";
    return result?true:message
}
//大于等于0小于100的整数
export function management(val){
	const regex=/^[0-9]\d{0,1}$/;
	let message="大于等于0小于100的整数";
	return regex.test(val)?true:message	
}
// 日期 大于等于1小于等于31的整数
export function dateDays(val){
    let message="大于等于1小于等于31的整数";
	return (val>=1 && val <=31 && Math.round(val) == val )?true:message	
}