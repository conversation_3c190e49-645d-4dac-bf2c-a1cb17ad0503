import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';

export function findInformationById(id){
    return request({
        url:`${process.env.VUE_APP_URL}/action/information/api/findInformationById/${id}?source=SMS&userCode=${util.encrypt(store.state.user.user.username)}`
    })
}

// 获取试题
export function queryMyTaskMJSF(){
    return request({
        url: `${process.env.VUE_APP_URL}/action/examWork/api/queryMyTaskMJSF?source=SMS&page=1&rows=99&size=99`,
        contentType: "application/json;charset=UTF-8"
    })
}

// 判断是否可以开始答题
export function findEffectiveExamByExamCodeMJSF(param){
    return request({
        url: `${process.env.VUE_APP_URL}/action/summary/api/findEffectiveExamSms?source=SMS&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCodes=${param.examCodes}`,
        contentType: "application/json;charset=UTF-8"
    })
}
