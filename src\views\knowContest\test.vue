<!-- 答题页 -->
<template>
    <div class="content">
      <div class="djs" v-if="djsdiv">
        <van-count-down :time="time" format="答题还剩：mm:ss" @change="nowTime"/>
      </div>
      <div class="wordBox">
         <div class="tit" v-show="workType == 'A'">每日答题</div>
         <div class="tit" v-show="workType == 'B'">挑战答题</div>
         <div class="tit" v-show="workType == 'C'">人人对战</div>
      </div>

      <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div class="question">{{questionsXH}}、{{item.questionName }}</div>
        <!-- 单选题 -->
        <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]" @change="selectA">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-radio :name="its.answerCode"  icon-size="18px" :class="((timeOver || jxShow) &&  its.isCorrect) ? 'myStyle' : ''">{{its.answerCode +'、' +its.answerContent }}</van-radio>
        </div>
        </van-radio-group>
        <!-- 多选题 -->
        <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
          <!-- {{ its.answerCode +'、' +its.answerContent }} -->
            <van-checkbox shape="square" :name="its.answerCode"  :value="its.answerCode"  :class="((timeOver || jxShow) &&  its.isCorrect) ? 'myStyle' : ''"  icon-size="18px">{{ its.answerCode +'、' +its.answerContent }}</van-checkbox>
        </div>
        </van-checkbox-group>
        <div  v-if="workType == 'A'" >
          <div class="jiexi"  @click="jxShow = !jxShow"  >点击查看答案>></div>
          <!-- <div v-show="jxShow" style="line-height:.2rem;color:#0a73ac" v-for="(its,indexs) in item.answerList" :key="indexs">
            <div v-show="its.isCorrect">{{its.correctExplanation}}</div>
          </div> -->
        </div>
        <div style="margin: 30px;">
          <van-button round block type="info" @click="next"  v-if="nextShow">下一题</van-button>
          <van-button round block type="info" @click="submit" v-if="submitShow">提交</van-button>
        </div>
      </div>
    </div>
</template>
<script>
import store from '@/store';
import { Dialog,Toast} from 'vant';
import { usDailyQuestions_getAnswersList,usDailyQuestions_saveRecord,usDailyQuestions_saveAnswer,
 usChallengeQuestions_getAnswersList,usChallengeQuestions_saveRecord,usChallengeQuestions_saveAnswer,usAnswerRecord_getRecordRankingById 
 ,saveRecord,getAnswersList,saveAnswer,findSurplusExam,permissionCheck,saveLastAnswer} from "@/api/test.js";
export default {
  name:'test',
  data() {
    return {
       user:store.state.user.user,//当前登陆人信息
       workType:'',//答题类型
       pmInsId:'',
       invitationId:'',
       answerRecordId:'',//答题记录表主键id 
       myIndex:0,
       result:[],
       time: 0,//时间
       timeOver:false,//时间结束
       jxShow:false,
       djsdiv:true,

       submitShow:false,
       nextShow:false,
       xh:0,
       context:'',//答案解析
       questionAll:[],
       questionAlls:[],
       stime:'',
       judge:'',
       timer:'',
       questionsXH:1
    }
  },
  mounted() {},
  created(){
    console.log(this.$route.params,'this.$route.params');
    if(JSON.stringify(this.$route.params) === '{}') {
      this.$router.replace('/knowContest/home')
      return
    }
     this.workType = this.$route.params.workType
     this.answerRecordId = this.$route.params.answerRecordId || ""
     this.pmInsId = this.$route.params.pmInsId || ""
     this.invitationId = this.$route.params.invitationId || ""
     if(this.workType == 'A' && !this.$route.params.answerRecordId){
         usDailyQuestions_saveRecord({workType:this.workType}).then(res=>{
          this.answerRecordId = res.data.id
          this.getlist(this.workType)
         })
     }else if(this.workType == 'A' && this.$route.params.answerRecordId){
          this.getlist(this.workType)
     }
     if(this.workType == 'B' && !this.$route.params.answerRecordId){
         usChallengeQuestions_saveRecord({workType:this.workType}).then(res=>{
          this.answerRecordId = res.data.id
          this.getlist(this.workType)
         })
     }else if(this.workType == 'B' && this.$route.params.answerRecordId){
          this.getlist(this.workType)
     }

     if(this.workType == 'C' && this.pmInsId){
        saveRecord({pmInsId:this.pmInsId}).then(res=>{
           this.getlist(this.workType)
        })
     }
     if(this.workType){
      this.nextShow = true
     }
     this.timer = Date.now()
     this.getLogin()
     this.judge = setInterval(()=>{
      this.getLogin()
     },10000)
  },
  beforeDestroy() { 
    clearInterval(this.judge);
  },
  methods:{
    selectA(val){
        // let myresult = []
        // if(val && this.workType=='A'){
        //   myresult[0] = []
        //   for(let u in this.questionAll[0].answerList){
        //     if(this.questionAll[0].answerList[u].isCorrect){
        //       myresult[0].push(this.questionAll[0].answerList[u].answerCode)
        //     }
        //   }
        //   if((Array.isArray(myresult[0])?myresult[0].join() : myresult[0])  == val){
        //     if(this.xh == this.questionAlls.length-1){
        //         this.submit()
        //       }else{
        //         this.next()
        //       }
        //   }
        // }
    },

    getLogin(){
        permissionCheck(this.timer).then(res=>{
          if(res.data == '0'){
             Toast.fail('您已在其他页面打开，本页面已关闭');
             this.$router.replace('/knowContest/home')
          }
        })
    },


      getlist(val){
        if(val=='A'){
           usDailyQuestions_getAnswersList().then(res=>{
            this.questionAlls = res.data
            // 计算序号
            this.questionsXH = (this.xh+1) + (5 - this.questionAlls.length)
            this.questionAll.push(this.questionAlls[this.xh])

            this.time = 30*1000
            if(this.xh == this.questionAlls.length-1){
              this.nextShow = false
              this.submitShow = true
            }
          })
        }
        if(val=='B'){
           usChallengeQuestions_getAnswersList().then(res=>{
            this.questionAlls = res.data
            // 计算序号
            this.questionsXH = (this.xh+1) + (20 - this.questionAlls.length)
            this.questionAll.push(this.questionAlls[this.xh])

            this.time = 30*1000

            if(this.xh == this.questionAlls.length-1){
              this.nextShow = false
              this.submitShow = true
            }
          })
        }
        if(val=='C'){
          if(this.$route.params.status == '0'){
            getAnswersList({invitationId:this.invitationId,pmInsId:this.pmInsId}).then(res=>{
              this.questionAlls = res.data
              // 计算序号
              this.questionsXH = (this.xh+1) + (10 - this.questionAlls.length)
              this.questionAll.push(this.questionAlls[this.xh])
              this.time = 300*1000
              if(this.xh == this.questionAlls.length-1){
                this.nextShow = false
                this.submitShow = true
              }
            })

          }else{
            findSurplusExam({invitationId:this.invitationId,pmInsId:this.pmInsId}).then(res=>{
              this.questionAlls = res.data
              this.questionAll.push(this.questionAlls[this.xh])
              this.time = this.questionAll[0].isStart=='1' ? (this.questionAll[0].sendTime || 300)*1000 : (this.questionAll[0].reciveTime || 300)*1000
              if(this.xh == this.questionAlls.length-1){
                this.nextShow = false
                this.submitShow = true
              }
            })
          }
          
        }
      },
      submit(){
        if((!this.result[0] || this.result[0].length == '0') && this.workType=='A'){
          this.result[0] = []
          for(let u in this.questionAll[0].answerList){
            if(this.questionAll[0].answerList[u].isCorrect){
              this.result[0].push(this.questionAll[0].answerList[u].answerCode)
            }
          }
        }

        let datas = {
          answerRecordId:this.answerRecordId,
          questionId:this.questionAll[0].id,
          questionCode:this.questionAll[0].questionCode,
          chosenAnswer: Array.isArray(this.result[0])?this.result[0].join() : this.result[0]
        }

        if(this.workType == 'A'){
          usDailyQuestions_saveAnswer(datas).then(res=>{
            if(res.status == '200'){
              usAnswerRecord_getRecordRankingById({ id: this.answerRecordId }).then(res2 => {
                let messages2 = `本次答题结束，您本次答对${res2.data.TRUENUM}道题目，得分${res2.data.TOTAL_SCORE}!`;
                let hasNavigated = false;
                Dialog.confirm({
                  title: '温馨提示', message:messages2 }).then(() => {
                    if (!hasNavigated) {
                      hasNavigated = true;
                      this.$router.replace('/knowContest/home');
                    }
                }).catch(err=>{});;

                // 5秒后自动关闭并跳转
                setTimeout(() => {
                  Dialog.close(); // 自动关闭弹窗
                  if (!hasNavigated) {
                      hasNavigated = true;
                      this.$router.replace('/knowContest/home');
                    }
                }, 5000);
              });


            }
          })  
        }
        if(this.workType == 'B'){
          usChallengeQuestions_saveAnswer(datas).then(res=>{
            if(res.status='200'){
              if(res.data == '0'){//答错直接跳出
                    // let answerRight =[]
                    // for(let u in this.questionAll[0].answerList){
                    //   if(this.questionAll[0].answerList[u].isCorrect){
                    //     answerRight.push(this.questionAll[0].answerList[u].answerCode)
                    //   }
                    // }
                    let answerRight = res.data.realAnswer[0].answerCode
                    usAnswerRecord_getRecordRankingById({ id: this.answerRecordId }).then(res2 => {
                      let message = `本次挑战答题结束，您本次答对${res2.data.TRUENUM}道题目，得分${res2.data.TOTAL_SCORE}, 正确答案为:${answerRight}`;
                      let hasNavigated = false;
                      Dialog.confirm({
                        title: '温馨提示',  message:message }).then(() => {
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }).catch(err=>{});

                      // 5秒后自动关闭并跳转
                      setTimeout(() => {
                        Dialog.close(); // 自动关闭弹窗
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }, 5000);
                    });

              }else{
                   usAnswerRecord_getRecordRankingById({ id: this.answerRecordId }).then(res2 => {
                      let messages = `本次挑战答题结束，您本次答对${res2.data.TRUENUM}道题目，得分${res2.data.TOTAL_SCORE}!`;
                      let hasNavigated = false;
                      Dialog.confirm({
                        title: '温馨提示',  message:messages }).then(() => {
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }).catch(err=>{});

                      // 5秒后自动关闭并跳转
                      setTimeout(() => {
                        Dialog.close(); // 自动关闭弹窗
                         if (!hasNavigated) {
                          hasNavigated = true;
                          this.$router.replace('/knowContest/home');
                        }
                      }, 5000);
                    });
              }
            }
          })  
        }

         if(this.workType == 'C'){
            datas.pmInsId = this.pmInsId
            datas.answerTime = this.stime
            datas.answerRecordId = this.questionAll[0].answerRecordId
            datas.invitationId = this.invitationId
            saveLastAnswer(datas).then(res=>{
              this.timeOver = true
              this.djsdiv = false
           
              let message1 = `本次人人对战已结束，稍后以短信方式发送考试结果。`;
              let hasNavigated = false;
              Dialog.confirm({
                title: '温馨提示',  message:message1 }).then(() => {
                  if (!hasNavigated) {
                    hasNavigated = true;
                    this.$router.replace('/knowContest/home');
                  }
              }).catch(err=>{
                 if (!hasNavigated) {
                    hasNavigated = true;
                    this.$router.replace('/knowContest/home');
                  }
              })

              // 5秒后自动关闭并跳转
              setTimeout(() => {
                Dialog.close(); // 自动关闭弹窗
                  if (!hasNavigated) {
                    hasNavigated = true;
                    this.$router.replace('/knowContest/home');
                  }
              }, 5000);
            }) 
        }

        
      },
      next(){
        if(this.workType !== 'C'){
            this.timeOver = true
            this.djsdiv = false
        }
        if((!this.result[0] || this.result[0].length == '0') && this.workType=='A'){
          this.result[0] = []
          for(let u in this.questionAll[0].answerList){
            if(this.questionAll[0].answerList[u].isCorrect){
              this.result[0].push(this.questionAll[0].answerList[u].answerCode)
            }
          }
        }

        let datas = {
          answerRecordId:this.answerRecordId,
          questionId:this.questionAll[0].id,
          questionCode:this.questionAll[0].questionCode,
          chosenAnswer: Array.isArray(this.result[0])?this.result[0].join() : this.result[0]
        }

        if(this.xh < this.questionAlls.length-1){
            this.xh++
             // 计算序号
            if(this.xh == this.questionAlls.length-1){
              this.nextShow = false
              this.submitShow = true
            }
            if(this.workType == 'A'){
              usDailyQuestions_saveAnswer(datas).then(res=>{
                if(res.status == '200'){
                    this.questionAll = []
                    this.result[0] = []
                    this.jxShow = false
                    this.timeOver = false
                    // 计算题号
                    this.questionsXH = (this.xh+1) + (5 - this.questionAlls.length)

                    this.questionAll.push(this.questionAlls[this.xh])
                    this.djsdiv = true
                    this.time = 30*1000
                    this.$forceUpdate()
                }
               
              })  
            }else if(this.workType == 'B'){
               usChallengeQuestions_saveAnswer(datas).then(res=>{
                if(res.status == '200'){
                  console.log(res.data,'res.data');
                  if(res.data.isCorrect == '0'){//答错直接跳出
                    // let answerRight =[]
                    // for(let u in this.questionAll[0].answerList){
                    //   if(this.questionAll[0].answerList[u].isCorrect){
                    //     answerRight.push(this.questionAll[0].answerList[u].answerCode)
                    //   }
                    // }

                    let answerRight = res.data.realAnswer[0].answerCode
                    usAnswerRecord_getRecordRankingById({ id: this.answerRecordId }).then(res2 => {
                      let messagess = `本次挑战答题结束，您本次答对${res2.data.TRUENUM}道题目，得分${res2.data.TOTAL_SCORE}, 正确答案为:${answerRight}`;
                      let hasNavigated = false;
                      Dialog.confirm({
                        title: '温馨提示', message:messagess }).then(() => {
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }).catch(err=>{});;

                      // 5秒后自动关闭并跳转
                      setTimeout(() => {
                        Dialog.close(); // 自动关闭弹窗
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }, 5000);
                    });
                  }else{
                    this.questionAll = []
                    this.result[0] = []
                    this.timeOver = false
                    // 计算题号
                    this.questionsXH = (this.xh+1) + (20 - this.questionAlls.length)
                    this.questionAll.push(this.questionAlls[this.xh])
                    this.djsdiv = true
                    this.time = 30*1000
                    this.$forceUpdate()
                  }
                }
              })  
            }else if(this.workType == 'C'){
              datas.pmInsId = this.pmInsId
              datas.answerTime = this.stime
              datas.answerRecordId = this.questionAll[0].answerRecordId
              datas.invitationId = this.invitationId
              saveAnswer(datas).then(res=>{
                this.questionAll = []
                this.result[0] = []
                // 计算题号
                this.questionsXH = (this.xh+1) + (10 - this.questionAlls.length)
                this.questionAll.push(this.questionAlls[this.xh])
                this.$forceUpdate()
              })
            }
        }
      },
      nowTime(timeData){
        const stime = parseInt(timeData.hours*60*60 +  timeData.minutes*60 + timeData.seconds)
        this.stime = stime
        if (stime !== 0 && stime % 4 === 0) {
        } else {
          if(stime=='1'){
            if(this.workType == 'A'){
              setTimeout(()=>{
                this.jxShow = true
              },1000)
              // if(!this.result[0]){
                setTimeout(()=>{
                  if(this.xh == this.questionAlls.length-1){
                    this.submit()
                  }else{
                    this.next()
                  }
                },500)
              // }
            }
            if(this.workType == 'B'){
              setTimeout(()=>{
                if(!this.result[0] || this.result[0].length=='0'){
                     let answerRight =[]
                    for(let u in this.questionAll[0].answerList){
                      if(this.questionAll[0].answerList[u].isCorrect){
                        answerRight.push(this.questionAll[0].answerList[u].answerCode)
                      }
                    }
                    usAnswerRecord_getRecordRankingById({ id: this.answerRecordId }).then(res2 => {
                      let messagess = `本次挑战答题结束，您本次答对${res2.data.TRUENUM}道题目，得分${res2.data.TOTAL_SCORE}, 正确答案为:${answerRight.join()}`;
                      let hasNavigated = false;
                      Dialog.confirm({
                        title: '温馨提示', message:messagess }).then(() => {
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }).catch(err=>{});;

                      // 5秒后自动关闭并跳转
                      setTimeout(() => {
                        Dialog.close(); // 自动关闭弹窗
                          if (!hasNavigated) {
                            hasNavigated = true;
                            this.$router.replace('/knowContest/home');
                          }
                      }, 5000);
                    });



                }else{
                    if(this.xh == this.questionAlls.length-1){
                      this.submit()
                    }else{
                      this.next()
                    }
                }
              },1000)
            }
            if(this.workType == 'C'){
              setTimeout(()=>{
                  this.submit()
              },1000)
            }
          }
        }
      },
  }
}

</script>

<style scoped>
.content{
	width: 100vw;
	min-height: 100vh;
	background: url("@/assets/images/zsjs/a1.jpg") no-repeat center center;
	background-size:  100% 100%;
	text-align: center;
	padding-top: 22vh;
}
.flex{
   display: flex;
   flex-direction: column;
   align-items: center;
}
.wordBox .tit{
  font-size: .18rem;
  font-weight: 700;
  margin: .1rem;
  margin-top: .4rem;
}


.van-radio{ margin-bottom: 0.08rem; align-items:flex-start }
.subject{ margin-bottom: 0.2rem;text-align: start; width: 75%; margin: 0 auto;}
.djs{ position: fixed;right: 0.4rem; z-index: 2;}
.question{ line-height: 0.22rem; margin-bottom: 0.1rem; font-weight: 700; }
.van-checkbox{ align-items: flex-start; margin-bottom: 3px; }
.van-radio__label{
  color: #333;
}
::v-deep .van-radio__icon .van-icon{
  border: 1px solid #898989;
  font-size: .14rem;
}
::v-deep .van-checkbox__icon .van-icon{
  border: 1px solid #898989;
  font-size: .14rem;
}
.jiexi{
  color: #0a73ac;
}

.van-count-down{
  color: red;
  font-weight:700;
}

.myStyle ::v-deep .van-radio__label{
  color: #04be02;
  font-weight: 700;
}
.myStyle ::v-deep .van-checkbox__label{
  color: #04be02;
  font-weight: 700;
}
</style>