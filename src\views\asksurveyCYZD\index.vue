<template>
  <div class="maintext">
    <div class="textDec">
      <h3>{{ examName }}</h3>
      <p>
        为深入了解各分公司纪委履行全面从严治党监督责任情况及纪检干部作风形象，提升纪委履职综合水平，公司纪委特开展此次问卷测评。本次问卷将采用不记名的形式，请您本着认真负责、实事求是的态度填写问卷，感谢您对公司纪委工作的支持与帮助！
      </p>
    </div>

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
      <!-- 题号和标题渲染 -->
      <div class="bigTit">
        <div v-show="i + 1 == 1">一、基本信息</div>
        <div v-show="i + 1 == 3">二、本单位纪委履职情况和纪检干部作风形象</div>
      </div>
      <!-- {{item.questionType}} -->
      <!-- <div v-if="item.questionType === 'more'" class="question">
        <span v-if="i==2" > {{ i - 1 + "、" + "(单选)" + item.questionName }}</span>
        <span v-else > {{ i - 1 + "、" + "(多选)" + item.questionName }}</span>
      </div>
      <div v-else-if="item.questionType === 'shortAnswer'"   class="question">{{ 20 + "、" + item.questionName }}</div>
      <div v-else  class="question">{{ 1 + "、" + item.questionName }}</div> -->

      <div class="question">{{(i+1+'、') + item.questionName }}</div>


      <!-- 单选题 -->
      <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]"  :class="'styleMY'+i">
          <div v-for="(its, indexs) in item.answerList" :key="indexs">
            <van-radio :name="its.answerCode" icon-size="18px"   >{{ its.answerCode + "、" + its.answerContent }}</van-radio>
          </div>
          <van-field autosize class="custom-field" v-if=" item.answerList[funZM(result[i])] ? item.answerList[funZM(result[i])].identification == '1' : false " 
          rows="3" type="textarea" v-model="zdytext[i]" placeholder="请填写" >
          </van-field>
      </van-radio-group>
      <!-- 多选题 -->
      <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]" >
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-checkbox
            shape="square"
            :name="its.answerCode"
            icon-size="18px"
            :disabled="its.disabled"
            @click="checkFun(item, its, i, indexs)"
            ref="checkboxes"
            >{{ its.answerCode + "、" + its.answerContent }}</van-checkbox
          >
        </div>
        <div v-for="it in result[i]" :key="it">
          <van-field
            autosize
            class="custom-field"
            v-if=" item.answerList[funZM(it)] ? item.answerList[funZM(it)].identification == '1' : false "
            rows="3"
            type="textarea"
            v-model="zdytext[i]"
            placeholder="请填写"
          ></van-field>
        </div>
      </van-checkbox-group>

      <!-- 简答题 -->
      <van-field
        v-if="item.questionType === 'shortAnswer'"
        autosize
        class="custom-field"
        v-model="result[i]"
        rows="3"
        type="textarea"
        placeholder="请填写"
      ></van-field>
    </div>

    <div style="margin: 16px">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import { Dialog } from "vant";
import { Notify } from "vant";
import {
  constructExamLayout,
  findExamInfo,
  submitExam,
  findEffectiveExamByExamCode,
} from "@/api/homes.js";

export default {
  name: "asksurvey",
  data() {
    return {
      examAppCode: this.$route.query.examAppCode
        ? this.$route.query.examAppCode
        : "",
      examCode: this.$route.query.examCode ? this.$route.query.examCode : "",
      singleQuestionList: [], //单选
      moreQuestionList: [], //多选
      shortAnswerQuestionList: [], //简答
      questionAll: {}, //全部试题
      zdytext: [], //自定义文本
      result: [], //提交结果
      time: 10000, //时间
      examAnswer: [],
      examRecord: [],
      stime: 0,
      id: "",
      IntX: "",
      truename: store.state.user.user.truename, //姓名
      examName: "", //名称
      examRemark: "", //简介
      resultOrg: [], //原始提交结果；

      duoxunList: [],
      myindex: "",
    };
  },
  mounted() {},
  created() {
    this.getList();
  },
  activated() {
    this.gettime();
  },
  methods: {
    // 多选单机事件
    checkFun(item, its, i, indexs) {
      // if (its.answerContent == "不存在") {
      //   if (this.result[i].includes(its.answerCode)) {
      //     this.result[i] = [its.answerCode];
      //     for (let i in item.answerList) {
      //       if (item.answerList[i].answerContent !== "不存在") {
      //         item.answerList[i].disabled = true;
      //       }
      //     }

      //     this.zdytext[i] = null
      //     this.$forceUpdate();
      //   } else {
      //     for (let i in item.answerList) {
      //       if (item.answerList[i].answerContent !== "不存在") {
      //         item.answerList[i].disabled = false;
      //       }
      //     }
      //     this.$forceUpdate();
      //   }
      // }
      // if (this.result[i]?.length > item.maxChooseNum) {
      //   for (let i in this.duoxunList) {
      //     if (this.duoxunList[i].id == its.id) {
      //       this.myindex = i;
      //     }
      //   }
      //   this.$refs.checkboxes[this.myindex].toggle();
      //   this.result[i] = this.result[i].slice(0, this.result[i].length - 1);
      //   return Notify({
      //     type: "warning",
      //     message: "最多选择" + item.maxChooseNum + "项",
      //   });
      // } else {
      //   this.result = this.sortNestedArrays(this.result)
      // }
    },
    funZM(str) {//字母转换
      if (str) {
        return JSON.stringify(str).charCodeAt(1) - 65;
      }
    },
    // 字母排序
    sortNestedArrays(array) {
      return array.map(subArray => {
        if (Array.isArray(subArray)) {
            return subArray.sort((a, b) => a.localeCompare(b));
        }else{
          return subArray
        }

      });
    },
    getList() {
      let data = { examAppCode: this.examAppCode };
      constructExamLayout(data).then((res) => {
        this.examName = res.data.examName;
        //  this.examRemark = res.data.examRemark
        this.singleQuestionList = res.data.singleQuestionList; //单选
        this.moreQuestionList = res.data.moreQuestionList; //多选
        this.shortAnswerQuestionList = res.data.shortAnswerQuestionList; //简答
        this.questionAll = this.singleQuestionList.concat(
          this.moreQuestionList,
          this.shortAnswerQuestionList
        );
        this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);

        this.duoxunList = this.moreQuestionList
          .map((a) => {
            return a.answerList.map((b) => b);
          })
          .flat();

        let Record = "";
        for (var i = 0; i < this.questionAll.length; i++) {
          Record += this.questionAll[i].questionCode + ",";
        }
        this.examRecord = Record.substring(0, Record.length - 1);
      });
    },
    gettime() {
      //获取考试信息
      // 判断是否可以开始答题
      findEffectiveExamByExamCode({ examCodes: this.examCode }).then((res) => {
          if (res.data.showFlag === true) {
            let data = {
              examAppCode: this.examAppCode,
              examCode: this.examCode,
              publishUsername: store.state.user.user.username,
            };
            findExamInfo(data).then((res) => {
              if (res.data) {
                if (!res.data.isFinishExam && !res.data.isMarkingExam) {
                  this.getList();
                } else {
                  this.$router.push({ name: "success" });
                }
              } else {
                this.getList();
              }
            });
          } else {
            Dialog.alert({
              title: "",
              message: "竞赛不在考试时间范围或者暂未权限！",
            }).then(() => {
              window.close();
              window.open("about:blank", "_self");
            });
          }
        })
        .catch(() => {
          Dialog.alert({
            title: "",
            message: "竞赛不在考试时间范围或者暂未权限！",
          }).then(() => {
            window.close();
            window.open("about:blank", "_self");
          });
        });
    },
    submit() {
      let arr = [];
      for(let i in this.result){
        if(this.result[i].length>0){
          arr.push(this.result[i])
        }
      }

      if (arr.length !== this.questionAll.length) {
        return Notify({
          type: "warning",
          message: "您有未完成的题目，请继续填写！",
        });
      }

      for (var i in this.result) {
        if (typeof this.result[i] == "string") {
          if (this.zdytext[i]) {
          } else {
            if (
              this.questionAll[i].answerList[this.funZM(this.result[i])] &&
              this.questionAll[i].answerList[this.funZM(this.result[i])]
                .identification == "1"
            ) {
              return Notify({
                type: "warning",
                message: "您有未完成的题目，请填写具体说明！",
              });
            }
          }
        } else if (typeof this.result[i] == "object") {
          if (this.zdytext[i]) {
          } else {
            for (var v in this.result[i]) {
              if (
                this.questionAll[i].answerList[this.funZM(this.result[i][v])]
                  .identification == "1"
              ) {
                return Notify({
                  type: "warning",
                  message: "您有未完成的题目，请填写具体说明！",
                });
              }
            }
          }
        }
      }
      Dialog.confirm({
        title: "温馨提示",
        message: "您已完成了所有题目，请确认是否进行提交",
        confirmButtonColor: "#1989fa",
      })
        .then(() => {
          for (var i in this.result) {
            if (typeof this.result[i] == "string") {
              if (this.zdytext[i]) {
                if (
                  this.questionAll[i].answerList[this.funZM(this.result[i])] &&
                  this.questionAll[i].answerList[this.funZM(this.result[i])].identification == "1"
                ) {
                  this.result[i] = this.result[i] + ":" + this.zdytext[i];
                }
              }
            } else if (typeof this.result[i] == "object") {
              if (this.zdytext[i]) {
                let index = this.result[i].length - 1;
                this.result[i][index] =
                  this.result[i][index] + ":" + this.zdytext[i];
              }
            }
          }

          let aboutResult = [];
          for (var j in this.result) {
            if (typeof this.result[j] !== "string") {
              aboutResult[j] = this.result[j].join("/");
            } else {
              aboutResult[j] = this.result[j];
            }
          }

          let data = {
            examAppCode: this.examAppCode,
            examCode: this.examCode,
            publishUsername: store.state.user.user.username,
            residueTime: this.stime,
            examAnswer: aboutResult.toString(),
            examRecord: this.examRecord,
          };

          let arrs = [];
          for (let u in this.questionAll) {
            if (this.questionAll[u].questionType == "more") {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                maxChooseNum: this.questionAll[u].maxChooseNum,
                examAnswer: aboutResult[u],
              });
            } else {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                examAnswer: aboutResult[u],
              });
            }
          }
          data.examInfoList = arrs;

          submitExam(data).then((res) => {
            if (res.status == 200) {
              clearInterval(this.IntX);
              this.result = [];
              this.zdytext = [];
              this.djsdiv = false;
              this.$router.push({ name: "success" });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style>
.maintext {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}
.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}
.subject {
  margin-bottom: 0.2rem;
}
.djs {
  position: fixed;
  top: 0.8rem;
  right: 0.3rem;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 2;
}
.question {
  line-height: 0.22rem;
  margin-bottom: 0.05rem;
  font-weight: 700;
}
.custom-field {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 5px;
}
.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}
.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}
.textDec p {
  text-indent: 2em;
}

.bigTit div {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 700;
}

/* .styleMY1{
  display: flex;
  flex-wrap: wrap;
} */

/* .styleMY1>div{
  width: 50%;
} */
</style>
