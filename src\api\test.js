import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';


//=============================================================每日答题
export function usDailyQuestions_answersRecordCheck(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usDailyQuestions/api/answersRecordCheck?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8'
    })
}

export function usDailyQuestions_getAnswersList(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usDailyQuestions/api/getAnswersList?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8'
    })
}

export function usDailyQuestions_saveRecord(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usDailyQuestions/api/saveRecord?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&workType=${params.workType}`,
        contentType:'application/json;charset=UTF-8'
    })
}


export function usDailyQuestions_saveAnswer(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usDailyQuestions/api/saveAnswer?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
        data:params
    })
}


//============================================================ 挑战答题
export function usChallengeQuestions_answersRecordCheck(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usChallengeQuestions/api/answersRecordCheck?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8'
    })
}

export function usChallengeQuestions_getAnswersList(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usChallengeQuestions/api/getAnswersList?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8'
    })
}

export function usChallengeQuestions_saveRecord(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usChallengeQuestions/api/saveRecord?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&workType=${params.workType}`,
        contentType:'application/json;charset=UTF-8'
    })
}


export function usChallengeQuestions_saveAnswer(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usChallengeQuestions/api/saveAnswer?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
        data:params
    })
}



// 获取答题正确次数及本次得分接口每日答题、挑战答题通用

export function usAnswerRecord_getRecordRankingById(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usAnswerRecord/api/getRecordRankingById?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&id=${params.id}`,
        contentType:'application/json;charset=UTF-8',
        data:params
    })
}

// 人人对战

// 判断人人对战答题次数
export function countExamWorkByTask(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usInvitations/api/countExamWorkByTask?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&sendUserName=${store.state.user.user.username}&recUserName=${params.recUserName}`,
        contentType:'application/json;charset=UTF-8',
    })
}
// 邀请答题
export function sendInvitation(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usInvitations/api/sendInvitation?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&sendUserName=${store.state.user.user.username}&recUserName=${params.recUserName}`,
        contentType:'application/json;charset=UTF-8',
    })
}

// 接受邀请
export function acceptInvitation(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usInvitations/api/acceptInvitation?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&invitationId=${params.invitationId}`,
        contentType:'application/json;charset=UTF-8',
    })
}

// 拒绝邀请
export function refauseInvitation(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usInvitations/api/refauseInvitation?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&invitationId=${params.invitationId}`,
        contentType:'application/json;charset=UTF-8',
    })
}
// 查询被邀请人列表信息
export function InvitedTask(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usPendingTask/api/InvitedTask?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&recUserName=${store.state.user.user.username}`,
        contentType:'application/json;charset=UTF-8',
    })
}
// 获取答题题目列表接口
export function getAnswersList(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usEveryOneQuestions/api/getAnswersList?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&invitationId=${params.invitationId}&pmInsId=${params.pmInsId}`,
        contentType:'application/json;charset=UTF-8',
    })
}
// 查询没有答完的题目信息
export function findSurplusExam(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usEveryOneQuestions/api/findSurplusExam?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&invitationId=${params.invitationId}&pmInsId=${params.pmInsId}`,
        contentType:'application/json;charset=UTF-8',
    })
}

// 开始答题
export function saveRecord(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usEveryOneQuestions/api/saveRecord?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&pmInsId=${params.pmInsId}&workType=C`,
        contentType:'application/json;charset=UTF-8',
    })
}

// 提交答案
export function saveAnswer(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usEveryOneQuestions/api/saveAnswer?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
        data:params

    })
}
// 最后一题的提交
export function saveLastAnswer(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usEveryOneQuestions/api/saveLastAnswer?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
        data:params

    })
}






// 获取当前排名
export function getSocreRanking(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usAnswerRecord/api/getSocreRanking?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&page=1&size=99`,
        contentType:'application/json;charset=UTF-8',
        loading:true
    })
}

// 多终端判断
// 多终端心跳-判断是否过期接口
export function permissionCheck(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usTerminalLoginInfo/api/permissionCheck?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&timestamp=${params}`,
        contentType:'application/json;charset=UTF-8',
    })
}
// 查询待接受或者已邀请
export function pendingList(params){
    return request({
        url:`${process.env.VUE_APP_URL}/action/usInvitations/api/getInvitationList?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
    })
}
















