<template>
  <div class="maintext">
    <div class="textDec">
      <h3>{{ examName }}</h3>
      <!-- <p>
        您好！为进一步推动公司各级领导干部担当作为，现组织对分公司开展"担当作为"专项调查。
      </p>
      <p>
        本问卷不记名，严格保密，请您按照自己的真实看法填写即可。感谢您的支持和参与！
      </p> -->
      <p v-html="examRemark"></p>
    </div>

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
      <!-- 题号和标题渲染 -->
      <!-- <div class="bigTit">
        <div v-show="i + 1 == 1">一、基本信息</div>
        <div v-show="i + 1 == 2">二、干部担当作为总体评价</div>
        <div v-show="i + 1 == 3">三、干部担当作为情况现状【第1题为单选，其他为多选，多选题最多选3项】</div>
      </div> -->
      <!-- {{item.questionType}} -->
      <!-- <div v-if="item.questionType === 'more'" class="question">
        <span v-if="i==2" > {{ i - 1 + "、" + "(单选)" + item.questionName }}</span>
        <span v-else > {{ i - 1 + "、" + "(多选)" + item.questionName }}</span>
      </div>
      <div v-else-if="item.questionType === 'shortAnswer'"   class="question">{{ 20 + "、" + item.questionName }}</div>
      <div v-else  class="question">{{ 1 + "、" + item.questionName }}</div> -->

      <div class="question" v-if="i == 0">{{(i+1+'、') + item.questionName.replace('[[]]','')  }}</div>
      <div class="question" v-else>{{(i+1+'、') + item.questionName }}
         <input type="text" class="filling"  v-model="result[i]">
      </div>
      <!-- 单选题 -->
      <van-radio-group v-if="item.questionType === 'filling'" v-model="result[i]"  :class="'styleMY'+i">
        <!-- 评分模式 -->
        <div >
          <div v-if="i == 0">
            <div class="slider-wrapper">
              <div class="slider-marks">
                <div class="mark-line" v-for="n in 21" :key="n" :class="{'mark-line-major': (n-1) % 2 === 0}"></div>
              </div>
              <van-slider 
                v-model="result[i]" 
                active-color="#1989fa" 
                max="10" 
                min="0" 
                step="0.5"
                bar-height="4px" 
                class="custom-slider"
                :formatter="value => value.toFixed(1)"
              >
                <template #button>
                  <div class="custom-button">{{ result[i]?result[i].toFixed(1):'0.0' }}</div>
                </template>
              </van-slider>
              <div class="slider-numbers">
                <span class="number-label" v-for="n in 11" :key="n">{{ n - 1 }}</span>
              </div>
              <div class="slider-text-labels">
                <div>不满意</div>
                <div>非常满意</div>
              </div>
            </div>
          </div>
          <div v-else>
            <!-- <div v-for="(its, indexs) in item.answerList" :key="indexs" >
              <van-radio :name="its.answerCode" icon-size="18px"   >{{ its.answerCode + "、" + its.answerContent }}</van-radio>
            </div>
            <van-field autosize class="custom-field" v-if=" item.answerList[funZM(result[i])] ? item.answerList[funZM(result[i])].identification == '1' : false " 
            rows="3" type="textarea" v-model="zdytext[i]" placeholder="请填写" >
            </van-field> -->
        </div>

        </div>
        
      </van-radio-group>
      <!-- 多选题 -->
      <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]" >
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-checkbox
            shape="square"
            :name="its.answerCode"
            icon-size="18px"
            :disabled="its.disabled"
            @click="checkFun(item, its, i, indexs)"
            ref="checkboxes"
            >{{ its.answerCode + "、" + its.answerContent }}</van-checkbox
          >
        </div>
        <div v-for="it in result[i]" :key="it">
          <van-field
            autosize
            class="custom-field"
            v-if=" item.answerList[funZM(it)] ? item.answerList[funZM(it)].identification == '1' : false "
            rows="3"
            type="textarea"
            v-model="zdytext[i]"
            placeholder="请填写"
          ></van-field>
        </div>
      </van-checkbox-group>

      <!-- 简答题 -->
      <van-field
        v-if="item.questionType === 'shortAnswer'"
        autosize
        class="custom-field"
        v-model="result[i]"
        rows="3"
        type="textarea"
        placeholder="请填写"
      ></van-field>
    </div>


    <div class="textDec">
       <p v-html="examRemarkFooter"></p>
    </div>



    <div style="margin: 16px">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import { Dialog } from "vant";
import { Notify } from "vant";
import {
  constructExamLayout,
  findExamInfo,
  submitExam,
  findEffectiveExamByExamCode,
} from "@/api/homes.js";

export default {
  name: "asksurvey",
  data() {
    return {
      examAppCode: this.$route.query.examAppCode
        ? this.$route.query.examAppCode
        : "",
      examCode: this.$route.query.examCode ? this.$route.query.examCode : "",
      singleQuestionList: [], //单选
      moreQuestionList: [], //多选
      shortAnswerQuestionList: [], //简答
      questionAll: [ {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:18",
                "modifiedTime": "2025-06-16 17:36:18",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882075012579328",
                "questionCode": "wyqk_2025_01-1",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 1,
                "questionGroupName": null,
                "questionName": "一、您对您所在部门班子“五要”落实情况整体评价如何？[[]]",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:18",
                "modifiedTime": "2025-06-16 17:36:18",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882076254093312",
                "questionCode": "wyqk_2025_01-2",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 2,
                "questionGroupName": null,
                "questionName": "二、您所在部门班子“五要”落实成效最好的是（单选）[[]]，落实成效最差的是（单选）[[]]。\nA.要马上就办（重在效率）\nB.要一盯到底（重在执行）\nC.要做深做透（重在质量）\nD.要敢于突破（重在创新）\nE.要干到最好（重在效果）",
                "questionType": "filling",
                "questionScore": "4",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:18",
                "modifiedTime": "2025-06-16 17:36:18",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882077071982592",
                "questionCode": "wyqk_2025_01-3",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 3,
                "questionGroupName": null,
                "questionName": "三、您所在部门班子目前在“要马上就办”方面，存在最突出的问题是（单选）[[]]，改善最明显的问题是（单选）[[]]。\nA.抓工作节奏慢、办事拖沓\nB.落实过程中不回应、不反馈\nC.工作完成后不报告、没回音",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:19",
                "modifiedTime": "2025-06-16 17:36:19",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882077923426304",
                "questionCode": "wyqk_2025_01-4",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 4,
                "questionGroupName": null,
                "questionName": "四、您所在部门班子目前在“要一盯到底”方面，存在最突出的问题是（单选）[[]]，改善最明显的问题是（单选）[[]]。\nA.不亲自研究，习惯做“甩手掌柜”、“传声筒”\nB.不给指导和资源，直接交给下级后无论做的好坏不管不问\nC.不跟踪问效，抓工作不闭环",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:19",
                "modifiedTime": "2025-06-16 17:36:19",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882078787452928",
                "questionCode": "wyqk_2025_01-5",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 5,
                "questionGroupName": null,
                "questionName": "五、您所在部门班子目前在“要做深做透”方面，存在最突出的问题是（单选）[[]]，改善最明显的问题是（单选）[[]]。\nA.没有看透想透，推动工作光有想法没办法\nB.存在“温吞水”现象，抓工作停留于表面，仅做表面文章\nC.工作干成“半拉子工程”",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:19",
                "modifiedTime": "2025-06-16 17:36:19",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882079559204864",
                "questionCode": "wyqk_2025_01-6",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 6,
                "questionGroupName": null,
                "questionName": "六、您所在部门班子目前在“要敢于突破”方面，存在最突出的问题是（单选）[[]]，改善最明显的问题是（单选）[[]]。\nA.遇到问题“等靠望”思想严重，不主动想办法解决\nB.存在路径依赖，习惯用老经验解决新问题\nC.主观能动性不强，攻坚克难意识不足",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            },
            {
                "orderByClause": null,
                "ssDate": null,
                "eeDate": null,
                "pageIndex": null,
                "pagesize": null,
                "createdTime": "2025-06-16 17:36:19",
                "modifiedTime": "2025-06-16 17:36:19",
                "enabled": true,
                "removedTime": null,
                "creator": "hadmin",
                "modifier": "hadmin",
                "id": "EQ876882080440008704",
                "questionCode": "wyqk_2025_01-7",
                "questionBankCode": "wyqk_2025_01",
                "questionOrder": 7,
                "questionGroupName": null,
                "questionName": "七、您所在部门班子目前在“要干到最好”方面，存在最突出的问题是（单选）[[]]，改善最明显的问题是（单选）[[]]。\nA.对工作要求不高，急于交卷、效果不好\nB.把干完了当做干好了\nC.忙忙碌碌而碌碌无为",
                "questionType": "filling",
                "questionScore": "2",
                "questionClass": null,
                "maxChooseNum": null,
                "answerList": [],
                "doneAnswer": null,
                "answerRecordId": null,
                "sendTime": null,
                "reciveTime": null,
                "isStart": null,
                "titleDescription": null,
                "answerListImport": null,
                "answer": null,
                "message": null,
                "type": null,
                "typeHtml": null
            }], //全部试题
      zdytext: [], //自定义文本
      result: [], //提交结果
      time: 10000, //时间
      examAnswer: [],
      examRecord: [],
      stime: 0,
      id: "",
      IntX: "",
      truename: 'hadmin', //姓名
      examName: "", //名称
      examRemark: "", //简介
      examRemarkFooter:"",
      resultOrg: {}, //原始提交结果；

      duoxunList: [],
      myindex: "",


    };
  },
  mounted() {},
  created() {
    // this.getList();
  },
  activated() {
    // this.gettime();
  },
  methods: {
    // 多选单机事件
    checkFun(item, its, i, indexs) {
      if (its.answerContent == "不存在") { //如果选项是不存在控制选项是否可编辑
        if (this.result[i].includes(its.answerCode)) {
          this.result[i] = [its.answerCode];
          for (let i in item.answerList) {
            if (item.answerList[i].answerContent !== "不存在") {
              item.answerList[i].disabled = true;
            }
          }

          this.zdytext[i] = null
          this.$forceUpdate();
        } else {
          for (let i in item.answerList) {
            if (item.answerList[i].answerContent !== "不存在") {
              item.answerList[i].disabled = false;
            }
          }
          this.$forceUpdate();
        }
      }
      // 判断一下选择的选项中是否有其他选项
      let ident = ''
      for(let i in item.answerList){
        if(item.answerList[i].identification){
          ident = item.answerList[i].answerCode
        }
      }
      // if (this.result[i]?.length > item.maxChooseNum) {
      if (this.result[i]?.filter(item => item !== ident).length > item.maxChooseNum) {
        for (let i in this.duoxunList) {
          if (this.duoxunList[i].id == its.id) {
            this.myindex = i;
          }
        }
        this.$refs.checkboxes[this.myindex].toggle();
        this.result[i] = this.result[i].slice(0, this.result[i].length - 1);
        return Notify({ type: "warning", message: "除其他选项外,最多选择" + item.maxChooseNum + "项", });
      } else {
        this.result = this.sortNestedArrays(this.result) //把字母排序一下确保PC渲染无问题
      }
    },
    funZM(str) {//字母转换
      if (str) {
        return JSON.stringify(str).charCodeAt(1) - 65;
      }
    },
    // 字母排序
    sortNestedArrays(array) {
      return array.map(subArray => {
        if (Array.isArray(subArray)) {
            return subArray.sort((a, b) => a.localeCompare(b));
        }else{
          return subArray
        }

      });
    },
    getList() {
      let data = { examAppCode: this.examAppCode };
      constructExamLayout(data).then((res) => {
        this.examName = res.data.examName;
         this.examRemark = res.data.examRemark
         this.examRemarkFooter = res.data.examRemarkFooter
        this.singleQuestionList = res.data.fillingQuestionList; //单选
        this.moreQuestionList = res.data.moreQuestionList; //多选
        this.shortAnswerQuestionList = res.data.shortAnswerQuestionList; //简答
        this.questionAll = this.singleQuestionList.concat(
          this.moreQuestionList,
          this.shortAnswerQuestionList
        );
        this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);

        this.duoxunList = this.moreQuestionList
          .map((a) => {
            return a.answerList.map((b) => b);
          })
          .flat();

        let Record = "";
        for (var i = 0; i < this.questionAll.length; i++) {
          Record += this.questionAll[i].questionCode + ",";
        }
        this.examRecord = Record.substring(0, Record.length - 1);
      });
    },
    gettime() {
      //获取考试信息
      // 判断是否可以开始答题
      findEffectiveExamByExamCode({ examCodes: this.examCode }).then((res) => {
          if (res.data.showFlag === true) {
            let data = {
              examAppCode: this.examAppCode,
              examCode: this.examCode,
              publishUsername: store.state.user.user.username,
            };
            findExamInfo(data).then((res) => {
              if (res.data) {
                if (!res.data.isFinishExam && !res.data.isMarkingExam) {
                  this.getList();
                } else {
                  this.$router.push({ name: "success" });
                }
              } else {
                this.getList();
              }
            });
          } else {
            Dialog.alert({
              title: "",
              message: "竞赛不在考试时间范围或者暂未权限！",
            }).then(() => {
              window.close();
              window.open("about:blank", "_self");
            });
          }
        }).catch(() => {
          Dialog.alert({
            title: "",
            message: "竞赛不在考试时间范围或者暂未权限！",
          }).then(() => {
            window.close();
            window.open("about:blank", "_self");
          });
        });
    },
    submit() {
      let arr = [];
      for(let i in this.result){
        if(this.result[i].length>0 || this.result[i].toString().length>0){
          arr.push(this.result[i])
        }
      }
      if (arr.length !== this.questionAll.length) {
        return Notify({
          type: "warning",
          message: "您有未完成的题目，请继续填写！",
        });
      }

      for (var i in this.result) {
        if (typeof this.result[i] == "string") {
          if (this.zdytext[i]) {
          } else {
            if (
              this.questionAll[i].answerList[this.funZM(this.result[i])] &&
              this.questionAll[i].answerList[this.funZM(this.result[i])]
                .identification == "1"
            ) {
              return Notify({
                type: "warning",
                message: "您有未完成的题目，请填写具体说明！",
              });
            }
          }
        } else if (typeof this.result[i] == "object") {
          if (this.zdytext[i]) {
          } else {
            for (var v in this.result[i]) {
              if (
                this.questionAll[i].answerList[this.funZM(this.result[i][v])]
                  .identification == "1"
              ) {
                return Notify({
                  type: "warning",
                  message: "您有未完成的题目，请填写具体说明！",
                });
              }
            }
          }
        }
      }
      Dialog.confirm({
        title: "温馨提示",
        message: "您已完成了所有题目，请确认是否进行提交",
        confirmButtonColor: "#1989fa",
      }).then(() => {
          this.resultOrg = this.deepCopy(this.result);
          for (var i in this.resultOrg) {
            if (typeof this.resultOrg[i] == "string") {
              if (this.zdytext[i]) {
                if (
                  this.questionAll[i].answerList[this.funZM(this.resultOrg[i])] &&
                  this.questionAll[i].answerList[this.funZM(this.resultOrg[i])].identification == "1"
                ) {
                  this.resultOrg[i] = this.resultOrg[i] + ":" + this.zdytext[i];
                }
              }
            } else if (typeof this.resultOrg[i] == "object") {
              if (this.zdytext[i]) {
                let index = this.resultOrg[i].length - 1;
                this.resultOrg[i][index] =
                  this.resultOrg[i][index] + ":" + this.zdytext[i];
              }
            }else if (typeof this.resultOrg[i] == "number") {
              this.resultOrg[i] = this.resultOrg[i].toString()
            }
          }

          let aboutResult = [];
          for (var j in this.resultOrg) {
            if (typeof this.resultOrg[j] !== "string") {
              aboutResult[j] = this.resultOrg[j].join("/");
            } else {
              aboutResult[j] = this.resultOrg[j];
            }
          }

          let data = {
            examAppCode: this.examAppCode,
            examCode: this.examCode,
            publishUsername: store.state.user.user.username,
            residueTime: this.stime,
            examAnswer: aboutResult.toString(),
            examRecord: this.examRecord,
          };

          let arrs = [];
          for (let u in this.questionAll) {
            if (this.questionAll[u].questionType == "more") {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                maxChooseNum: this.questionAll[u].maxChooseNum,
                examAnswer: aboutResult[u],
              });
            } else {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                examAnswer: aboutResult[u],
              });
            }
          }
          data.examInfoList = arrs;
          console.log(data);
          // return false
          submitExam(data).then((res) => {
            if (res.status == 200) {
              clearInterval(this.IntX);
              this.result = [];
              this.zdytext = [];
              this.djsdiv = false;
              this.$router.push({ name: "success" });
            }
          });
        })
        .catch(() => {});
    },

    // 使用Vue提供的工具函数deepCopy进行深拷贝
    deepCopy(obj) {
      return JSON.parse(JSON.stringify(obj));
    }

  },
};
</script>

<style>
.maintext {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}
.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}
.subject {
  margin-bottom: 0.2rem;
}
.djs {
  position: fixed;
  top: 0.8rem;
  right: 0.3rem;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 2;
}
.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
}
.custom-field {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 5px;
}
.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}
.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}
.textDec p {
  text-indent: 2em;
}

.bigTit div {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 700;
}

.styleMY1>div{
  display: flex;
  flex-wrap: wrap;
}



.slider-wrapper {
  position: relative;
  width: 95%; /* Overall width */
  margin: 20px auto;
  padding-top: 25px; /* Adjusted space for marks above */
  padding-bottom: 40px; /* Space for numbers and labels below */
}

.slider-marks {
  position: absolute;
  top: 0; /* Align with top padding of wrapper */
  left: 16px; /* Adjusted to align precisely with van-slider track start */
  width: calc(100% - 20px); /* Adjusted to align precisely with van-slider track end */
  display: flex;
  justify-content: space-between;
  z-index: 1;
  pointer-events: none;
  align-items:flex-end;
}

.mark-line {
  width: 1px; /* Thinner lines */
  background-color: #ccc; /* Lighter color */
  height: 6px; /* Default height */
}
.mark-line-major {
  height: 10px; /* Taller for major marks */
  background-color: #999; /* Darker for major marks */
}

.custom-slider {
  position: absolute; /* Changed to absolute for precise positioning */
  z-index: 2; /* Ensure slider is interactive */
  margin-top: 0; /* Remove previous margin */
  top: 19px; /* Position the slider to align its track visually between marks and numbers */
  left: 16px; /* Align with marks and numbers */
  width: calc(100% - 20px); /* Align with marks and numbers */
}

.custom-button {
  width: 20px; /* Adjusted width */
  height: 20px; /* Adjusted height */
  line-height: 20px; /* Center text vertically */
  color: #fff;
  font-size: 10px; /* Adjusted font size */
  text-align: center;
  background-color: #1989fa;
  border-radius: 30%; /* Perfect circle */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15); /* Slightly lighter shadow */
}

.slider-numbers {
  position: absolute;
  top: 31px; /* Position numbers below the slider and marks, adjusted for spacing */
  bottom: auto; /* Ensure bottom positioning is not used */
  left: 16px; /* Adjusted to align precisely with van-slider track start */
  width: calc(100% - 7px); /* Adjusted to align precisely with van-slider track end */
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666; /* Revert to original number color */
  z-index: 1;
  pointer-events: none;
}

.number-label {
  position: relative;
  transform: translateX(-50%); /* Center text under tick */
  white-space: nowrap;
}
.number-label:first-child {
  transform: translateX(0%); /* Align 0 to start */
}
.number-label:last-child {
  transform: translateX(-50%); /* Align 10 to end */
}

.slider-text-labels {
  position: absolute; /* Absolute position relative to wrapper */
  bottom: 0; /* Align with bottom padding */
  left: 16px; /* Align with track start */
  width: calc(100% - 32px); /* Align with track end */
  display: flex;
  justify-content: space-between;
  color: #666; /* Revert to original color */
  font-size: 12px; /* Revert to original font size */
  font-weight: normal; /* Remove bold */
}
.filling{
    border: none;
    border-bottom: 1px solid #000;
    padding: 0 5px;
    width: 100px;
}
</style>
