
import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';
// 获取待办
export function queryMyTask(data){
    return request({
        url: `${process.env.VUE_APP_URL}/action/examWork/queryMyTask2023?source=SMS&page=1&rows=99&size=99&examCode=${data.examCode}`,
        contentType: "application/json;charset=UTF-8"
    })
}

// 判断是否可以开始答题
export function findEffectiveExamByExamCodeMJSF(param){
    return request({
        url: `${process.env.VUE_APP_URL}/action/summary/api/findEffectiveExamByExamCodeMJSF?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCodes=${param.examCodes}`,
        contentType: "application/json;charset=UTF-8"
    })
}


