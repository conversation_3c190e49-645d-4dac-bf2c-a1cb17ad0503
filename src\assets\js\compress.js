export function compress(maxQ,minQ,nextQ,filei){                 
    return new Promise((resolve,reject)=>{
        let fr = new FileReader();
        fr.onload =(e)=> {                  
            let IMG = new Image();
            IMG.src = e.target.result;//读出来的文件流
            IMG.onload =async ()=> {             
                //将文件流画到canvas画布上
                let canvas = document.createElement('canvas');
                let ctx = canvas.getContext('2d');                                      
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                let resizeW = IMG.width*nextQ,resizeH = IMG.height*nextQ; 
                // var Orientation=filei.exifdata.Orientation;   
                let broInfo=this.getBrowserInfo();                        
                if(broInfo.browser=="firefox"){
                    canvas.width = resizeW; 
                    canvas.height = resizeH; 
                    ctx.drawImage(IMG, 0, 0, canvas.width, canvas.height);
                }else{
                    // switch(Orientation){
                    //     case 3://180°                                
                    //         canvas.width = resizeW; 
                    //         canvas.height = resizeH; 
                    //         ctx.rotate(180 * Math.PI / 180);
                    //         ctx.drawImage(IMG, -resizeW, -resizeH, resizeW, resizeH); 
                    //         break; 
                    //     case 6://顺时针90°    
                    //         var w=resizeW;
                    //         canvas.width = resizeH; 
                    //         canvas.height = w; 
                    //         ctx.rotate(90 * Math.PI / 180);
                    //         ctx.drawImage(IMG, 0, 0, resizeW, -resizeH); 
                    //         break;
                    //     case 8://逆时针90°   
                    //         var w=resizeW;
                    //         canvas.width = resizeH; 
                    //         canvas.height = w;  
                    //         ctx.rotate(270 * Math.PI / 180);
                    //         ctx.drawImage(IMG, -resizeW, 0, resizeW, resizeH); 
                    //         break;
                    //     default:  
                        
                    //     canvas.width = resizeW; 
                    //     canvas.height = resizeH;                       
                    //         ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                    //         break;
                    // }
                }
                //再将画布上元素导出到Base64
                let base64img = canvas.toDataURL(filei.type, nextQ);
                var arr = base64img.split(','),
                    mime = arr[0].match(/:(.*?);/)[1],
                    bStr = atob(arr[1]),
                    n = bStr.length,
                    u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bStr.charCodeAt(n);
                }
                var nFile=new File([u8arr], filei.name, {type: mime});
                if(!(nFile.size<(this.upload.maxSize*1024)  && nFile.size>(this.upload.minSize*1024))){//压缩后文件大于最大值
                    if(nFile.size>(this.upload.maxSize*1024)){
                        maxQ=nextQ;
                        nextQ=(nextQ+minQ)/2;//质量降低
                    }else if(nFile.size<(this.upload.minSize*1024)){//压缩后文件小于最小值
                        minQ=nextQ;
                        nextQ=(nextQ+maxQ)/2;//提高质量
                    }
                    nFile=await this.compress(maxQ,minQ,nextQ,filei);
                }                
                return resolve(nFile);
            };//end imgload
        };//end frload                     
        fr.readAsDataURL(filei);    
    });
}