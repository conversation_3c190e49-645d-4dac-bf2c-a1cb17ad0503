import Vue from 'vue'
import App from './App.vue'

Vue.config.productionTip = false

import 'vant/lib/index.css';
import Vant from 'vant';
Vue.use(Vant);
import { Toast } from 'vant';
Vue.use(Toast);
import VueWechatTitle from "vue-wechat-title";
Vue.use(VueWechatTitle);


import router from "./router";
import Router from 'vue-router';
import store from "./store";
import "@/permission";

import './assets/iconfont/iconfont.css';
import './assets/css/reset.css';
import './assets/css/public.css';

import util from "@/assets/js/public";
Vue.prototype.util = util;

import Bus from './components/bus';   
Vue.prototype.$bus = Bus

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app')

const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
