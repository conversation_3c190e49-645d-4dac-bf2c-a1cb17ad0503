<template>
    <div class="content">
      <van-form @submit="onSubmit">
      <van-field
         v-model="formValue.truename"
         name="姓名"
         label="姓名"
         placeholder="姓名"
         :rules="[{ required: true, message: '请填写姓名' }]"
      />
      <van-field
         v-model="formValue.phone"
         type="number"
         name="联系方式"
         label="联系方式"
         placeholder="联系方式"
         :rules="[{ required: true, message: '请填写联系方式' }]"
      />
      <van-field
         v-model="formValue.address"
         name="收货地址"
         label="收货地址"
         placeholder="收货地址"
         :rules="[{ required: true, message: '请填写收货地址' }]"
      />
   <div style="margin: 16px;">
    <van-button round block type="info" native-type="submit">提交</van-button>
   </div>
</van-form>
    </div>
</template>

<script>
import store from '@/store';
import { Dialog } from 'vant';
import { Notify } from 'vant';
import { constructExamLayout,saveExam,findExamInfo,submitExam,findEffectiveExamByExamCode,isLottery,createToJackpotReduce} from "@/api/homes.js";
import datas from "@/api/data.js";

export default {
  name:'asksurvey',
  data() {
    return {
      examAppCode:this.$route.query.examAppCode?this.$route.query.examAppCode:'',
      examCode:this.$route.query.examCode?this.$route.query.examCode:'',
      formValue:{}
    }
  },
  mounted() {
   //   console.log(this.$route.query,'1');
   //   isLottery({}).then((res)=>{
   //       if(res.data===false){
   //          Notify({ type: 'warning', message: '很遗憾，没有中奖' });
   //          this.$router.push({ name:'success', })
   //       }
   //   })
  },
  created(){
   //   console.log(this.$route.query,'2');
  },
  activated(){
   //   console.log(this.$route.query,'3');
  },
  methods:{
   onSubmit(values) {
      // console.log('submit', values);
      // console.log('formValue',this.formValue);
      this.formValue.username  = store.state.user.user.username
      createToJackpotReduce(this.formValue).then((res)=>{
         // console.log(res);
         Notify({ type: 'success', message: '提交成功！' });
         this.$router.push({ name:'success', })
      }).catch(()=>{})
   },
    
  }
}
</script>

<style scoped>
 .img{
    padding-top: 1.5rem;
    display: block;
    margin: 0 auto;
    padding-bottom: 0.3rem;
 }
 .content{
    line-height: 0.3rem;
    text-align:  center;
    font-weight: 400;
   
 }
 .fz16{
    font-size: 0.16rem;
 }
 .fz20{
    font-size: 0.20rem;
 }
</style>