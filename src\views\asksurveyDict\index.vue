<template>
  <div class="maintext">
    <div style="margin-bottom:50px">
      <img src="../../assets/images/home/<USER>" alt="" srcset=""/>
    </div>

    <div class="maintextBox">
      <div class="titbox" v-show="isShow">
        <div>商机名称：{{ appraiseWorkTitle }}</div>
        <div>被评价人：{{ appraiseTruename }}</div>
        <div>评分需满足“正态分布”，即10个评价项中至多有{{num_8_10}}个评价项可以在8-10分之间，至多有{{num_5_8}}个评价项可以在5-8分之间，至少有{{num_1_5}}个评价项在5分以下。</div>
      </div>
      <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div class="question" v-if="item.type === '0'">{{ (i + 1 + '、') + item.content }}</div>
        <div class="question" v-if="item.type === '1'">{{ item.content }}</div>
        <div class="showbox" v-if="item.type === '0'">
          <div class="box">非常不满意</div>
          <div class="box">非常满意</div>
        </div>
        <!-- 单选题 -->
        <van-radio-group v-if="item.type === '0'" v-model="result[i]" direction="horizontal" :disabled="!btnShow">
          <div v-for="(its,indexs) in item.templateItems" :key="indexs">
            <van-radio :name="its.code" icon-size="1px">{{ its.code }}</van-radio>
          </div>
        </van-radio-group>

        <!-- 简答题 -->
        <van-field v-if="item.type === '1'" autosize class="custom-field" :disabled="!btnShow"
                   v-model="result[i]" rows="3" type="textarea" placeholder="请填写"></van-field>
      </div>

      <div style="margin: 16px;" v-show="isShow && btnShow">
        <van-button round block type="info" @click="submit">提交</van-button>
      </div>

      <div class="imgDiv">
        <span>{{ title }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import store from '@/store';
import {Dialog} from 'vant';
import {Notify} from 'vant';
import {getInfo, submitInfoSalt} from "@/api/homes.js";
import {findDictValue} from "@/api/public";

export default {
  name: 'asksurveyDict',
  data() {
    return {
      workId: this.$route.query.workId ? this.$route.query.workId : '',
      singleQuestionList: [], //单选
      shortAnswerQuestionList: [], //简答
      questionAll: {}, //全部试题
      result: [],//提交结果
      time: 10000,//时间
      examAnswer: [],
      examRecord: [],
      stime: 0,
      id: '',
      IntX: '',
      appraiseWorkTitle: '',//商机名称
      appraiseTruename: '',//被评价人
      isShow: false,
      title: '',
      btnShow: true,
      num_1_5: 0,
      num_5_8: 0,
      num_8_10: 0,
    }
  },
  mounted() {
  },
  created() {
    this.getList();
    findDictValue('APPRAISE_SIN_TYPE').then(res => {
      if (res.data.length > 0) {
        this.num_1_5 = parseInt(res.data[0].value)
        this.num_5_8 = parseInt(res.data[1].value)
        this.num_8_10 = parseInt(res.data[2].value)
      }
    })
  },
  methods: {
    funZM(str) { //字母
      if (str) {
        return JSON.stringify(str).charCodeAt(1) - 65
      }
    },
    getList() {
      let data = {workId: this.workId, username: store.state.user.user.username}
      getInfo(data).then(res => {
        // if(res.data.status == '0'){
        this.isShow = true
        this.appraiseWorkTitle = res.data.appraiseWorkTitle
        this.appraiseTruename = res.data.appraiseTruename


        this.title = res.data.templates.score[0].title.split('-')[1]

        this.singleQuestionList = res.data.templates.score  //单选
        this.shortAnswerQuestionList = res.data.templates.write  //简答
        this.questionAll = this.singleQuestionList.concat(this.shortAnswerQuestionList);

        this.questionAll.sort((a, b) => a.displayOrder - b.displayOrder);

        let examRecordArry = []

        for (var i in this.questionAll) {
          examRecordArry.push(this.questionAll[i].id);
        }
        this.examRecord = examRecordArry.join(",");
        // }else{
        //   this.$router.push({ name:'success', })
        // }

      })
    },
    submit() {
      let arr = []
      arr = this.result.filter(item => item && item !== '');
      if (arr.length !== this.questionAll.length) {
        return Notify({type: 'warning', message: '您有未完成的评分，请继续填写！'});
      }

      let number_arr = this.result.filter(item => typeof(item) === 'number')
      let a_arr = number_arr.filter(a => a >= 8)
      let b_arr = number_arr.filter(b => b >= 5 && b < 8)
      let c_arr = number_arr.filter(c => c < 5)
      if (a_arr.length > this.num_8_10 || b_arr.length > this.num_5_8 || c_arr.length < this.num_1_5) {
        return Notify({type: 'warning', message: `评分需符合“正态分布”评价机制，即10个评价项中至多有${this.num_8_10}个评价项可以在8-10分之间，至多有${this.num_5_8}个评价项可以在5-8分之间，至少有${this.num_1_5}个评价项在5分以下。`});
      }
      let data = {
        workId: this.workId,
        contents: this.result.toString(),
        templateIds: this.examRecord,
      }

      Dialog.confirm({
        title: '温馨提示',
        message: '您已完成了所有评分，请确认是否进行提交',
        confirmButtonColor: '#1989fa'
      }).then(() => {
        this.btnShow = false
        submitInfoSalt({str: data, username: store.state.user.user.username}).then(res => {
          if (res.status == 200) {
            this.result = []
            this.$router.push({name: 'success',})
          }
        })
      }).catch(() => {
        this.btnShow = true
      });
    },
  }
}
</script>

<style>

.maintext {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  padding: 6vh .25rem .4rem;
  background: url("@/assets/images/home/<USER>") no-repeat center center;
  background-size: 100% 100%;
}

.maintextBox {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
  min-height: 300px;
}

.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start
}

.subject {
  margin-bottom: 0.2rem;
}

.djs {
  position: fixed;
  top: 0.8rem;
  right: 0.3rem;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 2;
}

.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
  border-bottom: 1px solid #dadada;
}

.custom-field {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 5px;
}

.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}

.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}

.textDec p {
  text-indent: 2em;
}

.imgDiv {
  width: calc(100% - 40px);
  height: 50px;
  background: url("@/assets/images/home/<USER>") no-repeat center center;
  background-size: 80% 80%;
  position: absolute;
  top: -22px;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  letter-spacing: 6px;
}

.titbox {
  margin: 15px 0;
  font-weight: 700;
  font-size: 16px;
  line-height: 30px;
}

.van-radio__label {
  margin-left: 1px;
  background-color: #f4f4f4;
  padding: 2px 5px;
}

.van-radio__icon {
  display: none;
}

.van-radio__icon--checked {
  display: none;
}

.van-radio__icon--checked + .van-radio__label {
  background-color: #3761e9;
  color: #fff;
  border-radius: 2px;
}

.van-radio--horizontal {
  margin-right: 0.07rem;
}

.showbox {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.showbox .box {
  background-color: #f4f4f4;
  padding: 2px 5px;
  margin: 5px 0 10px;
}

.van-radio-group--horizontal {
  display: flex;
  justify-content: space-between;
}
</style>
