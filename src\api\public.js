import request from "@/assets/js/request";
import store from "@/store";
import util from '@/assets/js/public';

// 根据出人或者部门
export function getOrgAndUser(params){
    var url = process.env.VUE_APP_URL+'/action/applyModel/api/getOrgAndUser?applyTypes=' + params.applyTypes + '&location=' + params.location + '&source=PC';
    if(params.processInstId){
        url = process.env.VUE_APP_URL+'/action/applyModel/api/getOrgAndUser?applyTypes=' + params.applyTypes + '&location=' + params.location + '&processInstId=' + params.processInstId + '&source=PC';
    }
    return request({
        url:url,
        contentType:'application/json;charset=UTF-8',
        data:params.appDecision  
    })
}

// 选人
export function findOneStep(params){
    return request({
        url:process.env.VUE_APP_URL+'/uums/sys/userinfo/findOneStep?appcode=' + params.appCode + '&orgCode=' + params.orgCode,
        contentType:'application/json;charset=UTF-8',
        data:{appCode:params.appCode}  
    })
}

// 选人搜索
export function findDimUserTree(params){
    return request({
        url:process.env.VUE_APP_URL+'/uums/sys/userinfo/findDimUserTree?appcode=exam',
        contentType:'application/json;charset=UTF-8',
        data:{truename:params.truename}  

    })
}

// 数据字典
export function findDictValue(dictType) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/sys/dictValue/findDictValue`,
        contentType: "application/json;charset=UTF-8",
        data: {
            dictType: dictType
        }
    });
}





