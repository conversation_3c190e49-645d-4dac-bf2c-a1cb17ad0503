{"name": "exam-h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --host=0.0.0.0", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.0", "core-js": "^3.8.3", "express": "^4.18.2", "js-cookie": "^2.2.1", "qs": "^6.11.0", "vant": "^2.9.4", "vue": "^2.6.14", "vue-router": "^3.0.1", "vue-wechat-title": "^2.0.7", "vuex": "^3.1.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "crypto-js": "^4.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "jsencrypt": "^3.0.0-rc.1", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}