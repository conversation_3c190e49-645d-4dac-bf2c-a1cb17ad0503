<template>
    <div>
        <div v-for="(item,index) in list " :key="index">
            <div v-if="item.treeType == 'org'" class="org">
                <i class="iconfont iconxiala color-8 font12">
                    <van-icon  v-if="item.children"  name="arrow-down" />
                    <van-icon v-else name="arrow" />
                </i>
                <div class="display-ib orgword" @click="orgFun(item)">
                    <span>{{item.name}}</span>
                </div>
            </div>
            <div v-if="item.treeType == 'user'" class="uList">
                <van-checkbox class="font14 ht48" :name="item.id" shape="square">{{item.name}}</van-checkbox>
            </div>
            <template v-if="item.children && item.children.length > 0">
                <checkbox-group class="checkGroup" :list="item.children"></checkbox-group>    
            </template>                       
        </div>
    </div>
</template>
 
<script>
import {findOneStep} from '@/api/public';
import { Notify } from "vant";
import Vue from 'vue';

export default {
    name: 'checkboxGroup',
    props:{
		list: Array
	},
    data () {
        return {
        }
    },
    created() {
    },
    methods: {
        orgFun(item){
            this.getOrgAndUser(item.id)
        },
        getOrgAndUser(orgCode) {
            console.log(orgCode,'orgCode');
            findOneStep({appCode:'exam',orgCode:orgCode}).then(res => {
                let myarr = []
                if (res.data.length>0) {
                    for(let i in this.list){
                        if(this.list[i].id == orgCode ){
                             myarr = this.list[i]
                            if(this.list[i].children){
                                delete myarr.children
                                Vue.set(this.list, i,myarr);
                            }else{
                                myarr.children = res.data
                                Vue.set(this.list, i, myarr);
                                // this.$emit('callBleck', res.data)
                                this.$bus.$emit('callBleck', res.data)
                            }
                        }
                    }
                    this.$forceUpdate()
                }else{
                   return Notify({ type: 'warning', message: "该组织无下级数据！" });
                }
            });
        }, 
    }
}
</script>

<style scoped>
    .org {
        width: 100%;
        background: #eee;
        padding: 0.10rem 0.16rem;
    }
    .orgword {
        font-size: 0.14rem;
        color: #888;
        margin-left: 0.13rem;
    }
    .ht48 {
        width: 100%;
        height: 0.48rem;
        line-height: 0.48rem;
        padding-left: 0.12rem;
        border-bottom: 1px solid #EEEEEE;
    }

    .checkGroup{
        padding: 10px;
    }
</style>