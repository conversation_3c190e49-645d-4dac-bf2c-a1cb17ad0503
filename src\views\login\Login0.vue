<template>
  <div v-wechat-title="title">
    <div class="outer_label" style="padding-top:0.9rem;">
        <img class="logo" :src="require('@/assets/images/logo.png')" style="width:0.63rem;height:0.61rem;">
        <div class="title">知识竞赛</div>
    </div>
    <div class="loginwrap">
        <input type="tel" class="user font16" placeholder="手机号" v-model="phone">
        <div class="pos-r wrapdiv">
            <input type="tel" class="messword font16" placeholder="短信验证码" v-model="smscode">
            <div @click="getMessage" class="getcatp font16 pos-a" :style="`${msm==0?'':'color:#dadada;'}`">{{getMessageBtn}}</div>
        </div>
        <van-button class="login_btn font18 bg-theme color-f mt74" @click.native="loginDo" round :loading="isBtnLoading">登录</van-button>
        <p class="font12 color-b text-c">切换为4G/5G网络并刷新页面可免验证进行自动登录</p>
        <p class="font12 color-b text-c" style="margin-top:-0.1rem;"><a href="#" @click="shuaxin" style="color:#0080CB">刷新页面</a></p>
        <!-- <van-button class="login_btn font18 border-theme color-theme mt24" @click.native="loginFastDo" round>手机快捷登录</van-button> -->
    </div>
  </div>
</template>
<script>
import Cookies from "js-cookie";
import util from '@/assets/js/public';
import { login,sendMes,getToken,loginFast } from '@/api/login';
import { Notify } from 'vant';
import Axios from "axios";
import Qs from "qs";
const phoneRule=/^(1)\d{10}$/;
export default {
    name:'login',
    data() {
      return {
        isBtnLoading: false,
        phone: '',
        smscode: '',
        msm:0,
        msmSetet:null,
        getMessageBtn:'获取验证码',
        submitFlag: true,//防止重复点击
      }
    },
    computed: {
      btnText() {
        if (this.isBtnLoading) return '登录中...';
        return '登录';
      },
      title(){
          return this.$route.meta.title+'-'+process.env.VUE_APP_APPNAME;
      }
    },
    created(){
        console.log(this.$route.query);
        this.$route.query.token =''
        console.log(this.$route.query);
    },
    methods: {
        loginDo() {
            if (!this.phone) {
                Notify({ type: 'warning', message: '请输入手机号' });
                return;
            }
            if (!this.smscode) {
                Notify({ type: 'warning', message: '请输入验证码' });
                return;
            }
            if(phoneRule.test(this.phone)){
                if(this.submitFlag){
                    this.submitFlag = false;
                    let phone=encodeURIComponent(util.encrypt(this.phone));
                    // let phone=util.encrypt(this.phone);
                    // let phone=this.phone;
                    this.$store.dispatch('Login',{phone,smscode:this.smscode}).then(res=>{
                        this.submitFlag = true;
                        // if(this.$route.fullPath.indexOf('home')!== -1){
                            // this.$router.push({path:'/home/<USER>',query: this.$route.query});   //网络强国
                            // this.$router.push({path:'/asksurveyDict',query: this.$route.query});   //DICT项目评分
                        // }else{
                        //     this.$router.push({path:'/detail',query: this.$route.query});
                        // }
                        // this.$router.push({path:'/detail'});
                        // this.$router.push({path:'/todolist'});

                        // console.log(this.$route.fullPath,'99999');
                        // console.log(this.$route.query,'1000000');


                        // if(this.$route.query.examAppCode=='2024_wlqg_zswd_sj'){
                        //     this.$router.push({path:'/home/<USER>',query: this.$route.query});   //网络强国
                        // }

                        // if(this.$route.query.workId){
                        //     this.$router.push({path:'/asksurveyDict',query: this.$route.query});   //DICT项目评分
                        // }

                        // if(this.$route.query.examAppCode=='2024_jjdc_sj'){
                        //     this.$router.push({path:'/asksurveyJjdc',query: this.$route.query});   //DICT项目评分
                        // }else{
                        //     this.$router.push({path:'/asksurveyMzcp',query: this.$route.query});   
                        // }

                        // this.$router.push({path:'/asksurveyCYZD',query: this.$route.query});   
                        // this.$router.push({path:'/asksurveyDdzw',query: this.$route.query});   
                        // this.$router.push({path:'/knowContest/home',query: this.$route.query});
                        // this.$router.push({path:'/asksurveyDict',query: this.$route.query});

                        // 巡察办
                        this.$router.push({path:'/asksurveyJJBM',query: this.$route.query});   


                    }).catch(error=>{
                        this.submitFlag = true;
                        this.isBtnLoading = false;
                    });
                }
            }else{
                Notify({ type: 'warning', message: '无效的手机号' });
            }
        },
        loginFastDo(){
            // Axios.defaults.timeout = 10000
            // Axios.defaults.baseURL = '/'
            // Axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            // Axios.defaults.withCredentials = true

            // // http request 拦截器
            // Axios.interceptors.request.use(
            // config => {
            //     config.headers = {
            //     'Content-Type': 'application/json' //  注意：设置很关键 
            //     }
            //     return config
            // },
            // err => {
            //     return Promise.reject(err)
            // }
            // )

            // Axios.post('http://10.92.82.161:8088/btdz/restLogin').then(res=>{
            //     alert(JSON.stringify(res));
            // }).catch(error=>{
            //     alert(JSON.stringify(error));
            // });
            // let gps=this.util.getQueryString();
            // this.$store.dispatch('Login',{token:gps.token}).then(res=>{
            //     this.$router.push({path:'/home'});
            // }).catch(error=>{
            // });            
                let phone=encodeURIComponent(util.encrypt(this.phone));
                this.$store.dispatch('Login',{phone}).then(res=>{
                    this.$router.push({path:'/detail'});
                });
            // login({token:gps.token}).then(res=>{
            // });
        },
        // 获取验证码
        getMessage(e) {
            if (!this.phone) {
                Notify({ type: 'warning', message: '请先输入手机号' });
            }else if(phoneRule.test(this.phone)) {
                // todo
                if(this.msm==0 && (!this.isBtnLoading)){
                    this.isBtnLoading=true;
                    if(this.msmSet){
                        clearInterval(this.msmSet);
                        this.msmSet=null;
                    }
                    sendMes(this.phone).then(res=>{
                        this.msm=60;
                        this.isBtnLoading=false;
                        this.msmSet=setInterval(()=>{
                            this.msm--;
                            if(this.msm==0){
                                clearInterval(this.msmSet);
                                this.msmSet=null;
                            }
                            this.getMessageBtn=this.msm==0?"获取验证码":`重新发送(${this.msm})`;
                        },1000);
                    }).catch(error=>{
                        this.isBtnLoading=false;
                    });
                }else{
                    Notify({ type: 'warning', message: '不要频繁点击，请稍后再试' });
                }
            }else{                
                Notify({ type: 'warning', message: '无效的手机号' });
            }
        },
        shuaxin(){
            location.reload();
        },

        // 测试用代码
        login2() {
           if (!this.phone || !this.password) { 
               Notify({ type: 'warning', message: '用户名与密码为必填项' });
           }else {
                var loginForm = {};
                loginForm.username=this.phone;
                loginForm.password=util.encrypt(this.password);
                loginForm.verifyCode='';
                this.$store.dispatch('Login',loginForm).then((res) => {                             
                    this.loading=false;
                    this.$router.push({path:'/home/<USER>'});
                }).catch((err) => {        
                    console.log(err);                                         
                    this.phone='';
                    this.password='';
                })
           }
        },

    }
  }
</script>
<style scoped>
    .mt24 {margin-top: 0.24rem;}
    .mt74 {margin-top: 0.60rem;}
    .logo {
        display: block;
        margin: 0 auto;
    }
    .title {
        color: #222;
        font-size: 0.24rem;
        margin-top: 0.19rem;
        text-align: center;
    }
    .loginwrap {
        margin-top: 0.54rem;
        width: 100%;
        box-sizing: border-box;
        padding: 0 0.32rem;
    }
    .user {
        width: 100%;
        height: 0.46rem;
        border: none;
        border-bottom: 1px solid #eee;
        outline:none;
        padding-left: 0.02rem;
    }
    .wrapdiv {
        width: 100%;
        height: 0.46rem;
        margin-top: 0.32rem;
    }
    .messword {
        width: 100%;
        height: 0.46rem;
        border: none;
        border-bottom: 1px solid #eee;
        outline:none;
        padding-left: 0.02rem;
        padding-right: 0.90rem;
        vertical-align: top;
    }
    .getcatp {
        color: #0080CB;
        top: 0.12rem;
        right: 0;
    }
    .login_logo {
        height: 100%;
    }
    .login_btn {
        width: 100%;
        height: 0.48rem;
    }
    .border-theme {
        border: 1px solid #0080CB;
    }
    p{line-height: 0.3rem;}
    input::-webkit-input-placeholder { /* WebKit browsers */ 
        color: #BBBBBB; 
    } 
    input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */ 
        color: #BBBBBB; 
    } 
    input::-moz-placeholder { /* Mozilla Firefox 19+ */ 
        color: #BBBBBB; 
    } 
    input:-ms-input-placeholder { /* Internet Explorer 10+ */ 
        color: #BBBBBB; 
    }
    .van-button__content {
        font-size: unset;
    }

</style>