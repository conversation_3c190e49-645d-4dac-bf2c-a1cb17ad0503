<template>
	<div class="content">
		<div v-if="pfObj.id" class="imgBox">
			<img @click="goExam()" class="imgBtn" src="@/assets/images/home/<USER>" alt="">
		</div>
		<div v-if="zyList.length>0" class="imgBox">
			<img class="imgBtn" src="@/assets/images/home/<USER>" alt="">
			<ul>
				<li v-for="(item,index) in zyList" :key="item.code" @click="goExam(index)"><img class="imgBtn" src="@/assets/images/home/<USER>" alt="">{{item.nameC}}</li>
			</ul>
		</div>
	</div>
</template>
<script>
import { Dialog } from 'vant';
import store from "@/store";
import { queryMyTaskMJSF,findEffectiveExamByExamCodeMJSF } from "@/api/detail.js";
export default {
	name:'detail',
	data() {
		return {
			pfObj: {},//普法赛道
			zyList: []//专业赛道
		}
   	},
	created(){
		this.getGroup();
	},
	methods:{
		// 获取赛道
		getGroup(){
			queryMyTaskMJSF().then(res => {
				if(res.data && res.data.content.length>0){
					for(let i in res.data.content){
						if(res.data.content[i].examCode.indexOf("pfz") > -1){
							this.pfObj = res.data.content[i];
						}else{
							res.data.content[i].nameC = this.getPathName(res.data.content[i].examAppCode);
							this.zyList.push(res.data.content[i]);
						}
					}
				}
			});
		},

		// 根据赛道编码获取赛道名称
		getPathName(code){
			let nameC = "";
			if(code.indexOf("flz") > -1){
				nameC = "法律组";
			}else if(code.indexOf("nsz") > -1){
				nameC = "内审组";
			}else if(code.indexOf("gcz") > -1){
				nameC = "工建组";
			}else if(code.indexOf("jjz") > -1){
				nameC = "纪检组";
			}
			return nameC;
		},

		// 进入对应考题
		goExam(index){
			let param;
			let examCodes = "";
			if(index || index===0){
				param = {
					id: this.zyList[index].id,
					examAppCode: this.zyList[index].examAppCode,
					examCode: this.zyList[index].examCode,
					username: store.state.user.user.username
				};
				examCodes = this.zyList[index].examCode;
			}else{
				param = {
					id: this.pfObj.id,
					examAppCode: this.pfObj.examAppCode,
					examCode: this.pfObj.examCode,
					username: store.state.user.user.username
				};
				examCodes = this.pfObj.examCode;
			}

			// // 判断是否可以开始答题
			findEffectiveExamByExamCodeMJSF({examCodes: examCodes}).then((res) => {
				if(res.data.showFlag === true){
					this.$router.push({path: "/home",query: param});
				}else{
					Dialog.alert({
						title: "",
						message: "竞赛尚未开始或者没有权限，请稍后~",
					}).then(() => {
					
					});
				}
			}).catch(() => {
				Dialog.alert({
					title: "",
					message: "竞赛尚未开始或者没有权限，请稍后~",
				}).then(() => {
				
				});
			});
		}
	}
}
</script>
<style>
.content{
	width: 100vw;
	height: 100vh;
	background: url("@/assets/images/home/<USER>") no-repeat center center;
	background-size:  100% 100%;
	text-align: center;
	padding-top: 38.5vh;
}
.imgBox{
	width: 100%;
	margin-bottom: .18rem;
}
.imgBox:last-child{
   margin-bottom: 0;
}
img.imgBtn{
	width: 1.43rem;
	height: .4rem;
}
.imgBox ul{
	width: 100%;
	height: 30vh;
	padding: 0 .2rem 0 37vw;
	overflow-y: auto;
}
.imgBox ul li{
	display: flex;
	align-items: center;
	font-size: .17rem;
	font-weight: bold;
	color: #f2f775;
	text-align: left;
	letter-spacing: .04rem;
	padding: .08rem 0;
}
.imgBox ul li img{
	width: .15rem;
	height: .16rem;
	margin-right: .04rem;
}

</style>