<!-- 我的待办 -->
<template>
    <div class="content">
      <div class="wordBox">
         <div v-if="jjbol">
            <!-- <img  style="width:75%;margin: 0.4rem 0;" src="@/assets/images/home/<USER>" alt=""/> -->
            <img  style="width:40%" @click="jjfun()"  src="@/assets/images/home/<USER>" alt=""/>
         </div>
      </div>
    </div>
</template>
<script>
import store from '@/store';
import { Dialog } from 'vant';
import { queryMyTask ,findEffectiveExamByExamCodeMJSF } from "@/api/todolist.js";
export default {
  name:'todolist',
  data() {
    return {
       user:store.state.user.user,//当前登陆人信息
       list:[],
       jjbol:false,  //是否显示问卷
       params:{
         examCode:'2023_GHHY_ks',
         examCode:this.$route.query.examCode?this.$route.query.examCode:''
       }
    }
  },
  mounted() {},
  created(){
     this.getlist()
  },
  activated(){ },
  methods:{
      getlist(){
         queryMyTask(this.params).then(res=>{
            if(res.status == '200'){
               for(var i in res.data.content){
                  if(res.data.content[i].examCode=='2023_GHHY_ks'){
                     this.list.push(res.data.content[i])
                     this.jjbol = true
                  }
               }
            }else{
               this.$router.push({path: "/login"});
            }
         }).catch(err=>{
            this.$router.push({path: "/login"});
         })
      },
      jjfun(){
         // 判断是否可以开始答题
			findEffectiveExamByExamCodeMJSF({examCodes: this.list[0].examCode}).then((res) => {
				if(res.data.showFlag === true){
               this.$router.push({path:'/asksurvey',query: {examAppCode:this.list[0].examAppCode,examCode:this.list[0].examCode}});
				}else{
					Dialog.alert({
						title: "",
						message: "测评不在考试时间范围或者暂未权限！",
					}).then(() => {
					
					});
				}
			}).catch(() => {
				Dialog.alert({
					title: "",
					message: "测评不在考试时间范围或者暂未权限！",
				}).then(() => {
				
				});
			});

      },
     
      
  }
}

</script>

<style scoped>
.content{
	width: 100vw;
	height: 100vh;
	background: url("@/assets/images/home/<USER>") no-repeat center center;
	background-size:  100% 100%;
	text-align: center;
	padding-top: 40vh;
}

.wordbox .txtImg{width: 80%;}

.flex{
   display: flex;
   flex-direction: column;
   align-items: center;
}
</style>