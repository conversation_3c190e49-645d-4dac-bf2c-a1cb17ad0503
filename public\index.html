<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
  <script>
    (function () {
      // 在标准 375px 适配下，100px = 1rem;
      var baseFontSize = 100;  
      var baseWidth = 375;
      var set = function () {
        var clientWidth = document.documentElement.clientWidth || window.innerWidth;
        var rem = 100;
        if (clientWidth != baseWidth) {
          rem = Math.floor(clientWidth / baseWidth * baseFontSize);
        }
        document.querySelector('html').style.fontSize = rem + 'px';
      }
      set();
      window.addEventListener('resize', set);
    }());
   
  </script>
</html>
