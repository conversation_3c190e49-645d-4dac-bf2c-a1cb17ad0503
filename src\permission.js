import router from "./router";
import store from "./store";
import { getToken } from "@/assets/js/auth";
import util from "@/assets/js/public";
import { getPhone } from "@/api/login";

const whiteList = ["/login","/login0","/asksurveyJJBM"];
router.beforeEach((to, from, next) => {
  let gps=to.query;
  console.log(gps,'gps')
if (whiteList.indexOf(to.path) !== -1) {
    next();
  }else if(gps.token){
    getPhone(gps.token).then(res=>{
      if(res.data.phone){ 
        store.dispatch('Login',{phone:encodeURIComponent(res.data.phone)}).then(ress=>{
          delete gps.token;
          next({ path: to.path,query:gps });     
        }).catch(error=>{
          next({ path: "/login" });
        });
      }else{
        next({ path: "/login"});
      }
    }).catch(err=>{
      next({ path: "/login"});
    });
  } else if (gps.phoneNumber) {
    //员工E单点
    store
      .dispatch('Login', { phone: encodeURIComponent(gps.phoneNumber) })
      .then((ress) => {
        delete gps.phoneNumber
        next({ path: to.path, query: gps })
      })
      .catch((error) => {
        next({ path: '/login' })
      })
  }else if(getToken()){
    if (to.path === "/login") {
      next({ path: "/" });
    } else {
      if(!store.getters.user){
        store.dispatch("GetInfo").then(res=>{
          next();
        }).catch(err => {
          store.dispatch("FedLogOut").then(outs => {
            Message.error(err || "验证失效,请重新登录!");
            next({ path: "/" });
          });
        });
      }else{
        next();
      }
      // console.log(1111111,store.getters.menus.length)
      // if (store.getters.menus.length === 0) {
      //   store.dispatch("GetInfo")
      //   .then(res => {
      //     if (router.options.routes.length > 3) router.options.routes.splice(3);
      //     store.dispatch('GetMenus').then(res => {
      //       // if(!res.data.authPermissions) res.data.authPermissions=[];
      //       // let datai = res.data.authPermissions.sort(function(a, b) {
      //       if(!res.data) res.data=[];
      //       // let datai = res.data.sort(function(a, b) {
      //       //   return a.displayOrder > b.displayOrder ? 1 : -1;
      //       // });
      //       // let datas = util.toTreeData(
      //       //   datai,
      //       //   "id",
      //       //   "parentId",
      //       //   "id,parentId,description,url,icon,menuLevel,permissionType"
      //       // );
      //       // let dynamicRoutes = util.getRoutesP(datas, 1);
      //       let dynamicRoutes = util.getRoutesP(res.data, 1);
      //       for (let i in dynamicRoutes) {
      //         router.options.routes.push(dynamicRoutes[i]);
      //       }
      //       store.state.user.menus = dynamicRoutes.length>0?dynamicRoutes:router.options.routes;
      //       router.addRoutes(dynamicRoutes);  
      //       next({ path: to.path,query:gps });
      //     }).catch(error =>{
      //     })
      //     // next();
      //   })
      //   .catch(err => {
      //     store.dispatch("FedLogOut").then(outs => {
      //       // Message.error(err || "验证失效,请重新登录!");
      //       next({ path: "/login" });
      //     });
      //   });
      // }else{
      //   next();
      // }
    }
  } else {
    console.log(2);
    next({ path: "/login",query:gps });
  }
  // next();
});
router.afterEach((e) => {
  
});
