<template>
	<div class="maintext">
	  <div v-if="!isFinishExam && initTime>0" class="djs">
		<van-icon name="clock"  color="#1989fa"/>
		<van-count-down :time="initTime" format="时间剩余：HH:mm:ss" @change="timeChange"/>
	  </div>
		  <img v-if="isFinishExam" class="goBack" src="@/assets/images/goBack.png" alt="" @click="goBack()">
		  <div class="subjectBox">
			  <div class="subject" v-for="(item, i) in questionAll" :key="i">
				  <van-form>
					  <h3 v-if="item.myOrder==0 && item.questionType=='single'">{{item.outerIndex}}、单选题（共{{singleQuestionList.length}}题，每题{{item.questionScore}}分）</h3>
					  <h3 v-else-if="item.myOrder==0 && item.questionType=='more'">{{item.outerIndex}}、多选题（共{{moreQuestionList.length}}道题，每题{{item.questionScore}}分，少选得{{util.divide(item.questionScore,2)}}分，多选错选不得分）</h3>
					  <h3 v-else-if="item.myOrder==0 && item.questionType=='judge'">{{item.outerIndex}}、判断题（共{{judgeQuestionList.length}}题，每题{{item.questionScore}}分）</h3>
					  <h3 v-else-if="item.myOrder==0 && item.questionType=='shortAnswer'">{{item.outerIndex}}、简答题（共{{shortAnswerQuestionList.length}}题，每题{{item.questionScore}}分）</h3>
					  <h3 v-else-if="item.myOrder==0 && item.questionType=='indefinite'">{{item.outerIndex}}、不定项题（共{{indefiniteQuestionList.length}}题，每题{{item.questionScore}}分），少选得{{util.divide(item.questionScore,2)}}分，多选错选不得分）</h3>
  
					  <div class="question" v-html="(item.myOrder+1+'、') + item.questionName"></div>
					  <!-- 单选题 -->
					  <van-radio-group v-if="item.questionType === 'single'" v-model="item.myAnswer" :disabled="isFinishExam">
						  <div v-for="(its,indexs) in item.answerList" :key="indexs">
							  <van-radio :name="its.answerCode" :class="isFinishExam&&its.isCorrect?'correct':(isFinishExam&&(its.answerCode==item.myAnswer)?'error':'')" icon-size="18px"><span v-html="its.answerCode +'、' +its.answerContent"></span></van-radio>
						  </div>
						  <!-- <van-field  autosize class="custom-field" v-if="item.answerList[funZM(item.myAnswer)]?item.answerList[funZM(item.myAnswer)].identification == '1':false"
						  rows="3" type="textarea" v-model="zdytext[i]"  placeholder="请填写"></van-field> -->
					  </van-radio-group>
					  <!-- 多选题 -->
					  <van-checkbox-group v-if="item.questionType === 'more'" v-model="item.myAnswer" :disabled="isFinishExam">
						  <div v-for="(its,indexs) in item.answerList" :key="indexs">
							  <van-checkbox shape="square" :name="its.answerCode" :class="isFinishExam&&its.isCorrect?'correct':(isFinishExam&&(item.myAnswer.indexOf(its.answerCode))>-1?'error':'')" icon-size="18px"><span v-html="its.answerCode +'、' +its.answerContent"></span></van-checkbox>
						  </div>
						  <!-- <div v-for="it in item.myAnswer" :key="it">
							  <van-field  autosize class="custom-field" v-if="item.answerList[funZM(it)]?item.answerList[funZM(it)].identification == '1':false"
								  rows="3" type="textarea" v-model="zdytext[i]"  placeholder="请填写"></van-field>
						  </div> -->
					  </van-checkbox-group>
					  <!-- 判断题 -->
					  <van-radio-group v-if="item.questionType === 'judge'" v-model="item.myAnswer" :disabled="isFinishExam">
						  <div v-for="(its,indexs) in item.answerList" :key="indexs">
							  <van-radio :name="its.answerCode" :class="isFinishExam&&its.isCorrect?'correct':(isFinishExam&&(its.answerCode==item.myAnswer)?'error':'')" icon-size="18px"><span v-html="its.answerContent"></span></van-radio>
						  </div>
					  </van-radio-group>
					  <!-- 简答题 -->
					  <van-field v-if="item.questionType === 'shortAnswer'" v-model="item.myAnswer" autosize class="custom-field" rows="3" type="textarea" placeholder="请填写"></van-field>
  
					  <!-- 不定项 -->
					  <van-checkbox-group v-if="item.questionType === 'indefinite'" v-model="item.myAnswer" :disabled="isFinishExam" >
						  <div v-for="(its,indexs) in item.answerList" :key="indexs">
							  <van-checkbox shape="square" :name="its.answerCode" :class="isFinishExam&&its.isCorrect?'correct':(isFinishExam&&(item.myAnswer.indexOf(its.answerCode))>-1?'error':'')" icon-size="18px"><span v-html="its.answerCode +'、' +its.answerContent"></span></van-checkbox>
						  </div>
					  </van-checkbox-group>
				  </van-form>
			  </div>
  
			  <div v-if="questionAll.length>0 && !isFinishExam" class="submitBtn">
				  <van-button round block type="info" @click="doSubmit()">提交</van-button>
			  </div>
		  </div>
	  </div>
  </template>
  <script>
  import store from '@/store';
  import { Dialog } from 'vant';
  import { Notify } from 'vant';
  import { constructExamLayout,saveExam,findExamInfo,submitExam,computeScore,findExamTime } from "@/api/home.js";
  export default {
	  name:'home',
	  data() {
		  return {
			  nowT: new Date().getTime(),
			  examAppCode: this.$route.query.examAppCode?this.$route.query.examAppCode:"",
			  examCode: this.$route.query.examCode?this.$route.query.examCode:"",
  
			  initTime: 0,//进入考试总时长（毫秒）
			  setTime: 0,//剩余考试时长（秒）
			  id: null,
			  isFinishExam: false,
			  canSubmit: true,
  
			  singleQuestionList: [],//单选题
			  moreQuestionList: [],//多选题
			  judgeQuestionList: [],//判断题
			  shortAnswerQuestionList: [], //简答题
			  indefiniteQuestionList: [], //不定项题
			  questionAll: [], //全部试题
  
			  examAnswer: [],
			  examRecord: [],
  
			  zdytext: [],//自定义文本
			  result: [],//提交结果
			  firstIn: true, //是否是第一次进入
  
			  docTime:''
  
  
		  }
	  },
	  mounted() {
	  },
	  created(){
		  findExamTime({examAppCode: this.examAppCode}).then(res => {
			  let datas = {
				  examAppCode: this.examAppCode,
				  examCode: this.examCode,
				  publishUsername: store.state.user.user.username
			  };
			  constructExamLayout({examAppCode: this.examAppCode}).then(data=>{
				findExamInfo(datas).then(ress=>{
				  if(!ress.data){//findExamInfo返回null
					  this.doConfirm(res.data.setTime);
				  }else{//findExamInfo正常返回
					if(ress.data.hasDrop){
						Dialog.alert({
							title: "",
							message: "答题已开始10分钟，未开始正式答题，视为自动弃考！",
						}).then(() => {
							this.$router.push({path: "/detail"});
						});
					}else{
						if(!ress.data.isFinishExam){ //考试未完成
						    this.doConfirm(res.data.setTime);
						}else{//考试已完成
							this.getList()
						}
					}
				  }
			  })	
              })
		  }).catch(err => {});
	  },
	  methods:{
		  doConfirm(num){
			  Dialog.confirm({
				  title: "温馨提示",
				  message: "每人仅有一次答题的机会，"+num+"分钟后自动交卷开始考试10分钟后未答题视为自动弃考，答题过程中请勿关闭或刷新页面，您可以点击“提交”按钮交卷，请确认是否开始正式答题？",
			  }).then(() => {
				  this.getList();
			  }).catch(() => {
				  // 返回赛道界面
				  this.$router.push({path: "/detail"});
			  });
		  },
  
		  // 获取试题
		  getList(notTip){
			  constructExamLayout({examAppCode: this.examAppCode}).then(res=>{
				  if(res.data){
					  // 考试总时长（秒）
					  this.initTime = parseFloat(res.data.setTime) * 60 * 1000;
					  this.setTimeFirst = parseFloat(res.data.setTime) * 60
					  // 单选
					  this.singleQuestionList = res.data.singleQuestionList;
					  for(var i in this.singleQuestionList){
						  this.singleQuestionList[i].myOrder = parseInt(i);
					  }
					  // 多选
					  this.moreQuestionList = res.data.moreQuestionList;
					  for(var i in this.moreQuestionList){
						  this.moreQuestionList[i].myOrder = parseInt(i);
					  }
					  // 判断
					  this.judgeQuestionList = res.data.judgeQuestionList;
					  for(var i in this.judgeQuestionList){
						  this.judgeQuestionList[i].myOrder = parseInt(i);
					  }
					  // 简答
					  this.shortAnswerQuestionList = res.data.shortAnswerQuestionList;
					  for(var i in this.shortAnswerQuestionList){
						  this.shortAnswerQuestionList[i].myOrder = parseInt(i);
					  }
  
					  // 不定项
					  this.indefiniteQuestionList = res.data.indefiniteQuestionList;
					  for(var i in this.indefiniteQuestionList){
						  this.indefiniteQuestionList[i].myOrder = parseInt(i);
					  }
  
					  this.questionAll = this.singleQuestionList.concat(this.moreQuestionList,this.judgeQuestionList,this.shortAnswerQuestionList,this.indefiniteQuestionList);
					  var outerIndex = 0;//大标题序号
					  for(var m in this.questionAll){
						  this.questionAll[m].questionName = this.questionAll[m].questionName.replace(/\(/g,"(&nbsp;&nbsp;&nbsp;&nbsp;");
						  if(this.questionAll[m].questionType == "more"){
							  this.questionAll[m].myAnswer = [];
						  }else if(this.questionAll[m].questionType == "indefinite"){
							  this.questionAll[m].myAnswer = [];
						  }else{
							  this.questionAll[m].myAnswer = "";
						  }
						  if(this.questionAll[m].myOrder == 0){
							  outerIndex++;
							  this.questionAll[m].outerIndex = this.util.formatNumber(0,outerIndex);
						  }
					  }
					  this.questionAll = JSON.parse(JSON.stringify(this.questionAll));
  
					  this.getNotFinish(notTip);//获取未完成考试
				  }
			  });
		  },
  
		  // 获取考试情况
		  getNotFinish(notTip){
			  let data = {
				  examAppCode: this.examAppCode,
				  examCode: this.examCode,
				  publishUsername: store.state.user.user.username
			  };
			  findExamInfo(data).then(res=>{
				  if(res.data){
					  this.id = res.data.id;
					  this.examRecord  = res.data.examRecord;
					  this.initTime = res.data.residueTime * 1000;
					  this.setTimeFirst = res.data.residueTime;
					  // this.initTime = 5 * 1000;
					  if(res.data.examAnswer){
						  let examRecord_new = res.data.examRecord.split(",");
						  let examAnswer_new = res.data.examAnswer.split(",");
						  for(var i in this.questionAll){
							  for(var j in examRecord_new){
								  if(this.questionAll[i].questionCode == examRecord_new[j]){
									  let myAnswer = examAnswer_new[j];
									  if(this.questionAll[i].questionType == "more"){
										  myAnswer = myAnswer.split('/');
									  }
									  if(this.questionAll[i].questionType == "indefinite"){
										  myAnswer = myAnswer.split('/');
									  }
									  this.questionAll[i].myAnswer = myAnswer;
								  }
							  }
						  }
					  }
					  // 完成考试
					  if(res.data.isFinishExam){
						  this.isFinishExam = res.data.isFinishExam;
						  if(!notTip){
							computeScore({examAppCode: this.examAppCode,examCode: this.examCode}).then((result) => {
								Dialog.alert({
									title: "",
									message: "您已完成竞赛答题，感谢您的参与！<br>您的分数是："+(result.data.score||0),
								}).then(() => {
								
								});
							});
						  }
					  }
  
				  }else{
					  // // 第一次答题
					  var examRecordArry = [];
					  for(var i in this.questionAll){
						  examRecordArry.push(this.questionAll[i].questionCode);
					  }
					  this.examRecord = examRecordArry.join(",");
					  this.saveTimefun(true);//保存考题
				  }
				  if(res.data){
					  if(this.firstIn && !res.data.isFinishExam){
					  var examRecordArry = [];
					  for(var i in this.questionAll){
						  examRecordArry.push(this.questionAll[i].questionCode);
					  }
					  this.examRecord = examRecordArry.join(",");
					  this.saveTimefun(true);//保存考题
					  this.firstIn = false
				  }
				  }
			  });
		  },
		  
		  // 定时保存考题及答案
		  saveTimefun(isFirst){
			  let param = {
				  id: this.id?this.id:'',
				  examAppCode: this.examAppCode,
				  examCode: this.examCode,
				  publishUsername: store.state.user.user.username,
				  residueTime: this.setTime,//剩余考试时长（毫秒）
				  examRecord: this.examRecord,//考题
				  // salt: this.util.encryptAES(this.nowT.toString())
				  salt: this.nowT
			  };
			  if(isFirst){
				  param.isVisit = 1;
				  param.residueTime = this.setTimeFirst
				  param.examAnswer = this.getAnswer();//答案
			  }else{
				  param.examAnswer = this.getAnswer();//答案
			  }
			  saveExam(param).then(res => {
				  if(isFirst) this.id = res.data.id;
			  }).catch(() => {
				  // Notify({ type: 'warning', message: '已在另一终端打开, 将在5秒后关闭' ,duration: 1500});
				  setTimeout(() => {
					  this.goBack();
				  },5000);
			  });
		  },
  
		  // 倒计时
		  timeChange(timeData){
			  if(!this.isFinishExam){
				  var sTime = parseInt(timeData.hours*60*60 +  timeData.minutes*60 + timeData.seconds);//秒
				  this.setTime = sTime;
				  if(this.setTime>0 && this.initTime!=this.setTime*1000 && sTime%5==0){  //5秒间隔
					  this.saveTimefun();//保存考题及答案
				  }
				  // 倒计时为0时也提交
				  if(this.setTime==0 && this.canSubmit){
					  this.doSubmit();
				  }
			  }
		  },
  
		  // 获取答案
		  getAnswer(){
			  var answerData = [];
			  for(var i in this.questionAll){
				  var answerIt = this.questionAll[i].myAnswer;
				  if(this.questionAll[i].questionType == "more" || this.questionAll[i].questionType == "indefinite"){
					  answerIt = answerIt.join("/");
				  }
				  answerData.push(answerIt);
			  }
			  return answerData.join(",");
		  },
  
		  // 提交
		  doSubmit(){
			  var lastAnswer = this.getAnswer();
			  let param = {
				  examAppCode: this.examAppCode,
				  examCode: this.examCode,
				  publishUsername: store.state.user.user.username,
				  examRecord: this.examRecord,
				  examAnswer: lastAnswer,
				  residueTime: this.setTime
			  };
  
			  if(this.setTime==0 && this.canSubmit){
				  this.canSubmit = false;
				  // 倒计时为0时提交
				  submitExam(param).then(res=>{
					  this.canSubmit = true;
					  this.isFinishExam = true;
					  this.getScore();
				  }).catch(() => {
					  this.canSubmit = true;
				  });
			  }else{
				  // 点提交按钮提交
				  var isComplete = true;
				  var lastAnswerArry = lastAnswer.split(",");
				  for(var i in lastAnswerArry){
					  if(lastAnswerArry[i] === ""){
						  isComplete = false;
						  break;
					  }
				  }
				  
				  if(!isComplete){
					  return Notify({ type: 'warning', message: '您有未完成的题目，请继续填写！' });
				  }else{
					  Dialog.confirm({
						  title: '温馨提示',
						  message: '您已完成了所有题目，请确认是否进行提交',
						  confirmButtonColor:'#1989fa'
					  }).then(() => {
						  if(this.canSubmit){
							  this.canSubmit = false;
							  submitExam(param).then(res=>{
								  this.canSubmit = true;
								  this.isFinishExam = true;
								  this.getScore();
								  this.getList(true);
							  }).catch(() => {
								  this.canSubmit = true;
							  });
						  }
					  }).catch(() => {});
				  }
			  }
		  },
  
		  // 获取分数
		  getScore(){
			  computeScore({examAppCode: this.examAppCode,examCode: this.examCode}).then((result) => {
				  Dialog.alert({
					  title: "",
					  message: "您已完成竞赛答题，感谢您的参与！<br>您的分数是："+(result.data.score||0),
				  }).then(() => {
				  
				  });
			  });
		  },
  
		  // 返回
		  goBack(){
			  this.$router.push({path: "/detail"});
		  },
		  
		  funZM(str){ //字母
			  if(str){
				  return JSON.stringify(str).charCodeAt(1) - 65 
			  }
		  },
	  }
  }
  </script>
  
  <style>
  .hide{display: none;}
  .maintext{position: relative;width: 100vw;height: 100vh;padding: 17vh .25rem .3rem;background:url("@/assets/images/home/<USER>") no-repeat center center;background-size:  100% 100%;}
  .djs{ display: flex;align-items: center;position: fixed;top: .25rem;right: 0;z-index: 2;height: .26rem;background-color: #fff;padding: 0 .12rem 0 .08rem;border-radius: .13rem 0 0 .13rem;}
  .djs .van-icon-clock{font-size: .16rem;}
  .goBack{
	  position: absolute;
	  right: .2rem;
	  top: .2rem;
	  width: .22rem;
  }
  .van-count-down{margin-left: .04rem;}
  .subjectBox{width: 100%;height: 100%;background-color: rgba(255,228,228,.8);box-shadow: 0 .04rem .2rem rgba(0,0,0,.5);padding: .28rem .28rem .1rem;margin: 0 auto;border-radius: .16rem;overflow-y: auto;}
  h3{font-size:15px;font-weight:bold;margin-bottom:.1rem;}
  .subject{margin-bottom: .15rem;}
  .subject div{font-size: .13rem;color: #2a2a2a;}
  .question{ line-height: 0.2rem; margin-bottom: 0.08rem; }
  .custom-field { border: 1px solid #e8e8e8; border-radius: 5px; padding: 5px; }
  .van-checkbox{ align-items: flex-start; margin-bottom: 3px; }
  .van-radio{margin-bottom: 0.05rem;align-items: flex-start;}
  .van-radio__label{color: #323233;line-height: 20px;}
  .van-checkbox__label{color: #323233;}
  .van-radio__icon .van-icon, .van-checkbox__icon .van-icon{border-color: #6c6c6c;background-color: none;}
  .van-radio__icon .van-icon{margin-top: 1px;}
  .van-checkbox__icon--checked .van-icon, .van-radio__icon--checked .van-icon{border-color: #1989fa}
  /* .correct .van-radio__label,.correct .van-checkbox__label{color: green;} */
  /* .error .van-radio__label,.error .van-checkbox__label{color: red;} */
  
  .van-radio__icon--disabled .van-icon, .van-checkbox__icon--disabled .van-icon{
	  background-color: transparent;
  }
  .van-radio__icon--disabled.van-radio__icon--checked .van-icon,
  .van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon{
	  color: #fff;
	  background-color: #1989fa;
  }
  
  .textDec{ color: #333; font-size: 14px; line-height: 22px; margin-bottom: 3px; }
  .textDec h3{ text-align: center; font-weight: bold; margin-bottom: 1px; }
  .textDec p{ text-indent: 2em; }
  .submitBtn{margin-top: .3rem;}
  </style>
  