<template>
  <div>
    <div class="nav bg-theme color-f text-c">
        <i class="iconfont iconfanhui font16 fl pl06" @click="goBack"><van-icon name="arrow-left" /></i>
        <span class="font17 ml16">选人</span>
    </div>
    <div class="content" :style="{bottom: bottomRem}">
        <van-field
        v-model="truename"
        center
        clearable
        label=""
        placeholder="请输入姓名进行搜索"
        >
        <template #button>
            <van-button size="small" type="info"  @click="search">搜索</van-button>
        </template>
        </van-field>
        
        <div v-if="singleSel=='true'" class="orgwrap">
            <van-radio-group @change="radioChange" v-model="radioResult">
                <radio-group :list="treeData"></radio-group>
            </van-radio-group>
        </div>
        
        <div v-else class="orgwrap">
            <van-checkbox-group @change="checkboxChange" v-model="checkboxResult">
                <checkbox-group :list="treeData"></checkbox-group>
            </van-checkbox-group>
        </div>
    </div>
    <div ref="btmDiv" class="bottom">
        <!-- 单选 -->
        <div v-if="singleSel == 'true'" class="leftdiv display-ib vertical-a-t">
            <div v-if="JSON.stringify(select) != '{}'" >
                <div @click="deleteSelect" class="select font12 display-ib pos-r" style="margin:0 0.18rem 0.10rem 0;">
                    {{select.name}}
                    <div class="pos-a bg-3 w30" style="right:-0.08rem;top:-0.06rem;">
                        <i class="iconfont icondel font12 color-f"></i>
                    </div>     
                </div>
            </div>
        </div>
        <!-- 多选 -->
        <div v-else class="leftdiv display-ib vertical-a-t">
            <div v-if="selectList.length>0">
                <div v-for="item in selectList" :key="item.name" class="display-ib">
                    <div @click="deleteSelect2(item.id)" class="select font12 display-ib pos-r" style="margin:0 0.18rem 0.10rem 0;">
                        {{item.name}}
                        <div class="pos-a bg-3 w30" style="right:-0.08rem;top:-0.06rem;">
                            <i class="iconfont icondel font12 color-f"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <van-button @click="confirmSelect" class="qbtn bg-theme color-f font14 fr">确定</van-button>
    </div>
  </div>
</template>

<script>
import { Notify } from 'vant';
import { Dialog,Toast} from 'vant';
import {getOrgAndUser,findOneStep,findDimUserTree} from '@/api/public';
import { countExamWorkByTask,sendInvitation } from "@/api/test.js";
import checkboxGroup from './checkboxGroup';
import radioGroup from './radioGroup';
import store from '@/store';

export default {
    name: 'HelloWorld',
    components: {
        checkboxGroup,radioGroup
    },
    data () {
        return {
            bottomRem: '', // content距离底部的距离
            singleSel: 'true', // 是单选还是多选
            requSel: 'true', // 是否必须选择
            checkboxResult: [], // 复选框，是个数组
            radioResult: '', // 单选框，暂定为字符串
            orgUserData: [],
            treeData: [],
            select: {}, // 单选-被选中的人
            selectList: [], // 多选-被选中的人
            group: '',
            truename:''
        }
    },
    created() {
        this.params = this.$route.params;
        // this.getOrgAndUser(store.state.user.user.belongOrgCode);
        // this.getOrgAndUser('4772276805467173986');
        this.getOrgAndUser('');
    },
    mounted() {
        this.getBottomH();
        this.$bus.$on('callBleck',this.callBleck)
    },
    beforeDestroy(){
        this.$bus.$off('callBleck')
    },
    methods: {
        search(){
           if(this.truename){
             findDimUserTree({truename:this.truename}).then(res=>{
             if (res.data.length>0) {
                    this.orgUserData = res.data;
                    var u = res.data;
                    var newO = []; // 组织
                    var newU = []; // 人
                    for (var i in u) {
                        if (u[i].treeType == 'org') {
                            newO.push(u[i]);
                        }
                        if (u[i].treeType == 'user') {
                            newU.push(u[i]);
                        }
                    }
                    var newOO = []; // 有孩子的组织
                    for (var i in newO) {
                        newO[i].hasChild = false;
                        for (var j in newU) {
                            if (newU[j].parentId !== newO[i].id) {
                                newO[i].hasChild = true; // 这个组织下面有孩子
                            }
                        }
                        // if (newO[i].hasChild == true) {
                            newOO.push(newO[i]);
                        // }
                    }
                    let newArr = newU.concat(newOO);

                    // 只渲染那种组织下面有孩子的
                    this.treeData = this.util.toTreeData(
                        newArr,
                        "id",
                        "parentId",
                        "id,name,treeType,parentId"
                    );
                }
           })

           }else{
             this.getOrgAndUser('');
           }
          
        },
        callBleck(data){
          this.orgUserData = this.orgUserData.concat(data)
        },
        goBack(){
            this.$router.back(-1);    //返回上一页  
            console.log(this.params);
            localStorage.setItem('searchParams', JSON.stringify(this.params));
        },

        getBottomH() {
            let height= this.$refs.btmDiv.offsetHeight;  
            this.bottomRem = height/100 + 'rem';
        },
        getOrgAndUser(belongOrgCode) {
            findOneStep({appCode:'exam',orgCode:belongOrgCode}).then(res => {
                if (res.data.length>0) {
                    this.orgUserData = res.data;
                    var u = res.data;
                    var newO = []; // 组织
                    var newU = []; // 人
                    for (var i in u) {
                        if (u[i].treeType == 'org') {
                            newO.push(u[i]);
                        }
                        if (u[i].treeType == 'user') {
                            newU.push(u[i]);
                        }
                    }
                    var newOO = []; // 有孩子的组织
                    for (var i in newO) {
                        newO[i].hasChild = false;
                        for (var j in newU) {
                            if (newU[j].parentId !== newO[i].id) {
                                newO[i].hasChild = true; // 这个组织下面有孩子
                            }
                        }
                        // if (newO[i].hasChild == true) {
                            newOO.push(newO[i]);
                        // }
                    }
                    let newArr = newU.concat(newOO);

                    // 只渲染那种组织下面有孩子的
                    this.treeData = this.util.toTreeData(
                        newArr,
                        "id",
                        "parentId",
                        "id,name,treeType,parentId"
                    );
                }
            });
        },
        radioChange(name) {
            for (var i in this.orgUserData) {
                if (this.orgUserData[i].id == name) {
                    this.select = this.orgUserData[i];
                    this.select.group = this.group;
                }
            }
        },
        deleteSelect() {
            this.select = {};
            this.radioResult = '';
        },
        deleteSelect2(name) {
            let index = this.selectList.findIndex(item => item.name == name);
            let index2 = this.checkboxResult.findIndex(item => item == name);
            if (index>-1) {
                this.selectList.splice(index, 1);
            }
            if (index2>-1) {
                this.checkboxResult.splice(index2, 1);
            }
            
        },
        checkboxChange(names) {
            this.selectList=this.orgUserData.filter(item=>names.includes(item.id));
            if (this.selectList.length>0) {
                this.selectList[0].group = this.group;
            }
            for (var i = 0; i < this.selectList.length; i++) {
                for (var j = i + 1; j < this.selectList.length;) {
                        if (this.selectList[i].id == this.selectList[j].id) {
                                this.selectList.splice(j, 1);
                        } else {
                            j++;
                        }
                    }
            }
        },
        confirmSelect() {
            if (this.requSel == 'true') { // 是否必须选择
                if (this.singleSel == 'true') { // 单选
                    if (JSON.stringify(this.select) != '{}') {
                        // localStorage.setItem('searchKeyword', JSON.stringify(this.select));
                        // localStorage.setItem('searchParams', JSON.stringify(this.params));
                        // window.opener = null;
                        // window.open("about:blank", "_top").close()
                        //this.$router.go(-1);  

                        Dialog.confirm({ title: "提示", message: "请确认是否进行邀请！" }).then(() => {
                            // 显示全局 loading
                            Toast.loading({
                            message: '加载中...',
                            forbidClick: true, // 禁止点击背景
                            duration: 0, // 一直显示，需要手动关闭
                            });
                           countExamWorkByTask({recUserName:this.select.id}).then(res=>{
                                if(res.data.answerdCount == res.data.everyoneAnswerCount){
                                    Toast.clear();
                                    Dialog.alert({ title: '温馨提示', message: '被邀请人，人人对战答题次数已用完，请明天再进行邀请!', }).then(() => {});
                                }else{
                                    sendInvitation({recUserName:this.select.id}).then(res2=>{
                                        if(res2.data){
                                            Toast.success('邀请已发送!');
                                            this.$router.replace('/knowContest/select')
                                            Toast.clear();
                                        }
                                    })

                                }
                            })
                        }).catch(() => {});
                    }else {
                        Notify({ type: 'warning', message: '请选择一个联系人' });
                    }
                }
                if (this.singleSel == 'false') { // 多选
                    if (this.selectList.length>0) {
                        localStorage.setItem('searchKeyword', JSON.stringify(this.selectList));
                        localStorage.setItem('searchParams', JSON.stringify(this.params));
                        // console.log(1);
                        // this.$router.go(-1);
                        let arr = []
                        for(let u in this.selectList ){
                            arr.push(this.selectList[u].id)
                        }
                        let mydata = {
                            id:this.$route.query.taskId,
                            userStr:arr.join(',')
                        }
                        Dialog.confirm({ title: "提示", message: "请确认是否进行转发！" }).then(() => {
                            forward(mydata).then((res) => {
                                // console.log(res,'123456789');
                                // this.$router.push({ name: "success" });
                                // window.opener = null;
                                // window.open("about:blank", "_top").close()

                                // window.opener=null;
                                // window.open('','_self');
                                // window.close();

                                this.moaBridge_ha_djfupt.closeDoc({
                                    refresh: true,
                                    appcode: "exam",
                                });
                            });
                        }).catch(() => {});
                    }else {
                        Notify({ type: 'warning', message: '请至少选择一个联系人' });
                    }
                }
            }else {
                if (this.singleSel == 'true') { // 单选
                    if (JSON.stringify(this.select) != '{}') {
                        localStorage.setItem('searchKeyword', JSON.stringify(this.select));
                    }
                }else if (this.singleSel == 'false'){
                    if (this.selectList.length>0) {
                        localStorage.setItem('searchKeyword', JSON.stringify(this.selectList));
                    }
                }
                localStorage.setItem('searchParams', JSON.stringify(this.params));
                // this.$router.go(-1);
            }
            
        },
  }
}
</script>

<style scoped>
    .content {
        position: absolute;
        top: 0.42rem;
        right: 0;
        left: 0;
        /* background: red; */
        overflow-y: auto;
    }
    .orgwrap {
        width: 100%;
    }
    .bottom {
        position: fixed;
        right: 0;
        bottom: 0;
        left: 0;
        min-height: 0.46rem;
        background: #eee;
    }
    .leftdiv {
        width: calc(100% - 0.90rem);
        padding: 0.10rem;
        min-height: 0.46rem;
        
    }
    .qbtn {
        width: 0.80rem;
        height: 0.38rem;
        line-height: 0.38rem;
        margin: 0.04rem 0.05rem;
        border-radius: 5px;
    }
    .select {
        padding: 0.04rem 0.07rem;
        border: 1px solid #0080cb;
        background: #e9f7ff;
        border-radius: 4px;
        color: #0080cb;
    }
    .w30 {
        width: 0.16rem;
        height: 0.16rem;
        background: #333;
        border-radius: 50%;
        text-align: center;
        line-height: 0.16rem;
    }
</style>
<style>
    .van-button__content {font-size:unset;}

     .nav{
        width: 100%;
        height: .4rem;
        line-height: .4rem;
        padding: 0 .16rem 0 .1rem;
        position: fixed;
        top: 0;
        z-index: 10;
    }
</style>
