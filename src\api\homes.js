import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from '@/store';

// 问卷答题  
export function constructExamLayout(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examAttribute/api/constructExamLayout?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}

export function findExamInfo(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/findExamInfo?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}

export function saveExam(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/saveExam2?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}


export function submitExam(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/examInfo/api/submitExam?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}

//判断是否可以考试
export function findEffectiveExamByExamCode(param){
    return request({
        url: `${process.env.VUE_APP_URL}/action/summary/api/findEffectiveExamSms?source=SMS&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCode=${param.examCodes}`,
        contentType: "application/json;charset=UTF-8"
    })
}

export function findEffectiveExam2023(param){
    console.log(store.state.user.user.username);
    return request({
        url: `${process.env.VUE_APP_URL}/action/summary/api/findEffectiveExam2023?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCode=${param.examCode}`,
        contentType: "application/json;charset=UTF-8"
    })
}

export function findEffectiveExamByExamCodeMJSF(param){
    console.log(store.state.user.user.username);
    return request({
        url: `${process.env.VUE_APP_URL}/action/summary/api/findEffectiveExamByExamCodeMJSF?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCodes=${param.examCode}`,
        contentType: "application/json;charset=UTF-8"
    })
}

// 抽奖相关
export function isLottery(param){
    return request({
        url: `${process.env.VUE_APP_URL}/action/lottery/api/isLottery?source=MOBILE&currentUserCode=${util.encrypt(store.state.user.user.username)}&examCode=${param.examCodes}`,
        contentType: "application/json;charset=UTF-8"
    })
}
// 抽奖信息提交
export function createToJackpotReduce(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/lottery/api/createToJackpotReduce?source=SMS`,
        contentType:'application/json;charset=UTF-8',
        data:data
    })
}
// DICT项目支撑评分表+++++++++++++++++++++++++++++++++++++++++++++++

export function getInfo(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/appraise/work/getInfo?source=SMS&username=${data.username}&workId=${data.workId}`,
        contentType:'application/json;charset=UTF-8',
    })
}

export function submitInfoSalt(data){    
    return request({
        url:`${process.env.VUE_APP_URL}/action/appraise/work/submitInfoSalt?source=SMS&username=${data.username}&currentUserCode=${util.encrypt(store.state.user.user.username)}`,
        contentType:'application/json;charset=UTF-8',
        data:{data:util.encryptAES(JSON.stringify(data.str))}
    })
}














