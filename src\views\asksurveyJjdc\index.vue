<!-- 民主评议表问卷 -->
<!-- 特殊：都是单元，优化选项样式（分列）；单选题分类，重新加序号 -->
<template>
  <div class="maintext">
    <div class="titles" >2023年分公司全面从严治党监督责任履行及纪检干部作风建设情况测评问卷</div>
    <div class="textDec">
      <p>为深入了解各分公司纪委履行全面从严治党监督责任情况及纪检干部作风形象，提升纪委履职综合水平，公司纪委特开展此次问卷测评。本次问卷将采用不记名的形式，请您本着认真负责、实事求是的态度填写问卷，感谢您对公司纪委工作的支持与帮助！</p>
    </div>
    <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div class="blockTit">{{item.type}}</div> 
        <div class="question">{{(i+1)+'、' + item.questionName }}</div>
        <!-- <div class="question">{{ item.questionName }}</div> -->
        <!-- 单选题 -->
        <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]" direction="horizontal" :disabled="!btnShow">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-radio :name="its.answerCode"  icon-size="18px">{{its.answerCode +'、' +its.answerContent }}</van-radio>
        </div>
          <van-field  autosize class="custom-field" v-if="item.answerList[funZM(result[i])]?item.answerList[funZM(result[i])].identification == '1':false"
           rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
        </van-radio-group>
        <!-- 多选题 -->
        <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]" :disabled="!btnShow">
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-checkbox shape="square" :name="its.answerCode" icon-size="18px">{{ its.answerCode +'、' +its.answerContent }}</van-checkbox>
        </div>
          <div v-for="it in result[i]" :key="it">
            <van-field  autosize class="custom-field" v-if="item.answerList[funZM(it)]?item.answerList[funZM(it)].identification == '1':false"
                rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
          </div>
        </van-checkbox-group>

       <!-- 简答题 -->
        <van-field v-if="item.questionType === 'shortAnswer'" autosize class="custom-field"  :disabled="!btnShow"
           v-model="result[i]"  rows="3" type="textarea" placeholder="请填写"></van-field>
    </div>

    <div style="margin: 16px;" v-if='btnShow'>
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
</div>
</template>
<script>
import store from '@/store';
import { Dialog } from 'vant';
import { Notify } from 'vant';
import { constructExamLayout,saveExam,findExamInfo,submitExam,findEffectiveExamByExamCodeMJSF} from "@/api/homes.js";

import datas from "@/api/datas.js";


export default {
  name:'asksurveyJjdc',
  data() {
    return {
      examAppCode:this.$route.query.examAppCode?this.$route.query.examAppCode:'',
      examCode:this.$route.query.examCode?this.$route.query.examCode:'',
      singleQuestionList: [], //单选
      moreQuestionList:[],    //多选
      shortAnswerQuestionList:[], //简答
      questionAll:{}, //全部试题
      zdytext:[],//自定义文本
      result:[],//提交结果
      time: 10000,//时间
      examAnswer:[],
      examRecord:[],
      stime:0,
      id:'',
      datas:datas,
      btnShow:true
    }
  },
  mounted() {
  },
  created(){
  },
  activated(){
    this.gettime()
  },
  methods:{
       funZM(str){ //字母转换
            if(str){
                return JSON.stringify(str).charCodeAt(1) - 65 
            }
        },
      getList(){
          let data = {examAppCode:this.examAppCode}
          constructExamLayout(data).then(res=>{
              this.singleQuestionList = res.data.singleQuestionList  //单选
              this.moreQuestionList = res.data.moreQuestionList  //多选
              this.shortAnswerQuestionList = res.data.shortAnswerQuestionList  //简答
              this.questionAll = this.singleQuestionList.concat(this.moreQuestionList, this.shortAnswerQuestionList);
              this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);

              let examRecordArry = []

              for(var i in this.questionAll){
                examRecordArry.push(this.questionAll[i].questionCode);
              }  
              this.examRecord = examRecordArry.join(",");

              // // 数据序号处理
              // // var jsonArray = this.datas.data.singleQuestionList
              // var jsonArray = this.questionAll
              // // 用于记录每个 type 的序号
              // const typeIndex = {};
              // // 遍历 JSON 数组
              // jsonArray.forEach(obj => {
              //   const { type } = obj;
              //   // 检查 typeIndex 对象中是否有该 type 的属性
              //   if (typeIndex.hasOwnProperty(type)) {
              //     // 如果有，则将序号加一，并添加到当前对象的 index 属性中
              //     typeIndex[type] += 1;
              //     obj.index = typeIndex[type];
              //   } else {
              //     // 如果没有，则初始化序号为 1，并添加到当前对象的 index 属性中
              //     typeIndex[type] = 1;
              //     obj.index = typeIndex[type];
              //   }
              // });
              // this.questionAll = jsonArray
          })
      },
      gettime(){ //获取考试信息
        // 判断是否可以开始答题
        findEffectiveExamByExamCodeMJSF({examCode: this.examCode}).then((res) => {
				if(res.data.showFlag === true){
          let data = {examAppCode:this.examAppCode,examCode:this.examCode,publishUsername:store.state.user.user.username}
          findExamInfo(data).then(res=>{
            if(res.data){
              if(!res.data.isFinishExam && !res.data.isMarkingExam){
                this.getList();
              }else{
                this.$router.push({ name:'success', })
              }
            }else{
                this.getList(); 
            }
          })
				}else{
					Dialog.alert({
						title: "",
						message: "竞赛不在考试时间范围或者暂未权限！",
					}).then(() => {
            window.close()
            window.open('about:blank', '_self');
					});
				}
			}).catch(() => {
				Dialog.alert({
					title: "",
					message: "竞赛不在考试时间范围或者暂未权限！",
				}).then(() => {
          window.close()
          window.open('about:blank', '_self');
				});
			});
      },

      submit(){
        let arr = [] 
        arr = this.result.filter(item => item && item !== '' );
        if(arr.length !== this.questionAll.length){
            return  Notify({ type: 'warning', message: '您有未完成的题目，请继续填写！' });
        }

        for(var i in this.result){
            if (typeof this.result[i] == 'string') {
                if (this.zdytext[i]) {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        this.result[i] = this.result[i] + ':' + this.zdytext[i]
                    }
                } else {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                    }
                }
            }else if(typeof this.result[i] == 'object') {
                if (this.zdytext[i]) {
                    let index =  this.result[i].length-1
                    this.result[i][index] = this.result[i][index] + ':' + this.zdytext[i]
                } else {
                    for(var v in this.result[i]){
                    if (this.questionAll[i].answerList[this.funZM(this.result[i][v])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                     }
                    }
                }
            }
        }

        for(var j in this.result){
            if(typeof this.result[j] !== 'string'){
                this.result[j] = this.result[j].join('/')
            }
        }

        let data = {
            examAppCode:this.examAppCode,
            examCode:this.examCode,
            publishUsername:store.state.user.user.username,
            residueTime:this.stime,
            examAnswer:this.result.toString(),
            examRecord:this.examRecord,
        }
        Dialog.confirm({
        title: '温馨提示',
        message: '您已完成了所有题目，请确认是否进行提交',
        confirmButtonColor:'#1989fa'
        }).then(() => {
            this.btnShow = false
            submitExam(data).then(res=>{
            if(res.status==200){
                this.result = []
                this.zdytext = []
                this.djsdiv = false
                this.$router.push({ name:'success', })
            }
        }) }).catch(() => {
          this.btnShow = true
        });
    },
  }
}
</script>

<style>
.maintext{ margin-left: .3rem; margin-right: .3rem; }
.van-radio{ margin-bottom: 0.05rem; align-items:flex-start;}
.subject{ margin-bottom: 0.2rem; }
.djs{ position: fixed; top: 0.8rem; right: 0.3rem; background: #fff; display: flex; align-items: center; z-index: 2; }
.question{ line-height: 0.22rem; margin-bottom: 0.1rem; font-weight: 700; }
.custom-field { border: 1px solid #e8e8e8; border-radius: 5px; padding: 5px; }
.van-checkbox{ align-items: flex-start; margin-bottom: 3px; }

.textDec{ color: #333; font-size: 14px; line-height: 22px; margin-bottom: 3px; }
.textDec h3{ text-align: center; font-weight: bold; margin-bottom: 1px; }
.textDec p{ text-indent: 2em; }

.van-radio__label{
  width: 100%;
}
.titles{
  font-weight: 700;
  text-align: center;
  font-size: 16px;
  margin: 10px 0;

}
.blockTit{
  font-weight: 700;
  font-size: 15px;
  margin: 10px 0;
}


</style>
