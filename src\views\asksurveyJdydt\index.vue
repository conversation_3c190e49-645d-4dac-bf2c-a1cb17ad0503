<template>
  <div class="maintext">
      <div class="textDec">
        <h3>{{examName}}</h3>
        <p>{{examRemark}}</p>
      </div> 

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
        <div v-if="item.questionType === 'more'" class="question">{{(i+1+'、') +'(多选)'+item.questionName }}</div>
        <div v-else class="question">{{(i+1+'、')+item.questionName }}</div>


        <!-- 单选题 -->
        <van-radio-group v-if="item.questionType === 'single'" v-model="result[i]">
          <div v-for="(its,indexs) in item.answerList" :key="indexs">
              <van-radio :name="its.answerCode"  icon-size="18px">{{its.answerCode +'、' +its.answerContent }}</van-radio>
          </div>
          <van-field 
            autosize class="custom-field" v-if="item.answerList[funZM(result[i])]?item.answerList[funZM(result[i])].identification == '1':false"
           rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写">
          </van-field>
        </van-radio-group>
        <!-- 多选题 -->
        <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]"  >
        <div v-for="(its,indexs) in item.answerList" :key="indexs">
            <van-checkbox shape="square" :name="its.answerCode" icon-size="18px"  @click="checkFun(item,its,i, indexs)" ref="checkboxes">{{ its.answerCode +'、' +its.answerContent }}</van-checkbox>
        </div>
          <div v-for="it in result[i]" :key="it">
            <van-field  autosize class="custom-field" v-if="item.answerList[funZM(it)]?item.answerList[funZM(it)].identification == '1':false"
                rows="3" type="textarea"  v-model="zdytext[i]"  placeholder="请填写"></van-field>
          </div>
        </van-checkbox-group>

       <!-- 简答题 -->
        <van-field v-if="item.questionType === 'shortAnswer'" autosize class="custom-field"
           v-model="result[i]"  rows="3" type="textarea" placeholder="请填写"></van-field>
    </div>

    <div style="margin: 16px;">
      <van-button round block type="info" @click="submit">提交</van-button>
    </div>
</div>
</template>
<script>
import store from '@/store';
import { Dialog } from 'vant';
import { Notify } from 'vant';
import { constructExamLayout,saveExam,findExamInfo,submitExam,findEffectiveExamByExamCode,isLottery,createToJackpotReduce} from "@/api/homes.js";
import datas from "@/api/data.js";
import { login } from '@/api/login';

export default {
  name:'asksurvey',
  data() {
    return {
      examAppCode:this.$route.query.examAppCode?this.$route.query.examAppCode:'',
      examCode:this.$route.query.examCode?this.$route.query.examCode:'',
      singleQuestionList: [], //单选
      moreQuestionList:[],    //多选
      shortAnswerQuestionList:[], //简答
      questionAll:{}, //全部试题
      zdytext:[],//自定义文本
      result:[],//提交结果
      time: 10000,//时间
      examAnswer:[],
      examRecord:[],
      stime:0,
      id:'',
      IntX:'', 
      datas:datas,
      truename:store.state.user.user.truename,//姓名
      examName:'',//名称
      examRemark:'',//简介
      resultOrg:[],//原始提交结果；

      duoxunList:[],
      myindex:''
    }
  },
  mounted() {
    
  },
  created(){
      this.getList();
  },
  activated(){
    this.gettime()
  },
  methods:{
    // 多选单机事件
      checkFun(item,its,i,indexs){
        if(this.result[i].length>item.maxChooseNum){
          for(let i in this.duoxunList){
            if(this.duoxunList[i].id == its.id){
              this.myindex = i
            }
          }
          this.$refs.checkboxes[this.myindex].toggle();
          this.result[i] = this.result[i].slice(0,this.result[i].length-1)
          return Notify({ type: 'warning', message: "最多选择"+ item.maxChooseNum +"项" });
        }else{
          console.log(this.result);
        }
      },
       funZM(str){ //字母转换
            if(str){
                return JSON.stringify(str).charCodeAt(1) - 65 
            }
        },
      getList(){
          let data = {examAppCode:this.examAppCode}
          constructExamLayout(data).then(res=>{
             this.examName = res.data.examName
             this.examRemark = res.data.examRemark
              this.singleQuestionList = res.data.singleQuestionList  //单选
              this.moreQuestionList = res.data.moreQuestionList  //多选
              this.shortAnswerQuestionList = res.data.shortAnswerQuestionList  //简答
              this.questionAll = this.singleQuestionList.concat(this.moreQuestionList, this.shortAnswerQuestionList);
              this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);
              // this.questionAll = this.datas.data.singleQuestionList.concat(this.datas.data.moreQuestionList, this.datas.data.shortAnswerQuestionList);

              this.duoxunList = this.moreQuestionList.map(a => {
                return a.answerList.map(b => b)
              }).flat()

              let Record =''
              for (var i = 0; i < this.questionAll.length; i++) {
                  Record += this.questionAll[i].questionCode + ',';
              }
              this.examRecord = Record.substring(0, Record.length - 1);
          })
      },
      gettime(){ //获取考试信息
        // 判断是否可以开始答题
        findEffectiveExamByExamCode({examCodes: this.examCode}).then((res) => {
				if(res.data.showFlag === true){
          let data = {examAppCode:this.examAppCode,examCode:this.examCode,publishUsername:store.state.user.user.username}
          findExamInfo(data).then(res=>{
            if(res.data){
              if(!res.data.isFinishExam && !res.data.isMarkingExam){
                this.getList();
              }else{
                this.$router.push({ name:'success', })
              }
            }else{
              this.getList();
            }
          })
				}else{
					Dialog.alert({
						title: "",
						message: "竞赛不在考试时间范围或者暂未权限！",
					}).then(() => {
					      window.close()
                window.open('about:blank', '_self');
					});
				}
			}).catch(() => {
				Dialog.alert({
					title: "",
					message: "竞赛不在考试时间范围或者暂未权限！",
				}).then(() => {
          window.close()
          window.open('about:blank', '_self');
				});
			});
      },
      submit(){
        let arr = [] 
        arr = this.result.filter(item => item && item !== '' );
        if(arr.length !== this.questionAll.length){
            return  Notify({ type: 'warning', message: '您有未完成的题目，请继续填写！' });
        }

        for(var i in this.result){
            if (typeof this.result[i] == 'string') {
                if (this.zdytext[i]) {
                    // if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                    //     this.result[i] = this.result[i] + ':' + this.zdytext[i]
                    // }
                } else {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                    }
                }
            }else if(typeof this.result[i] == 'object') {
                if (this.zdytext[i]) {
                    // let index =  this.result[i].length-1
                    // this.result[i][index] = this.result[i][index] + ':' + this.zdytext[i]
                } else {
                    for(var v in this.result[i]){
                      // console.log(this.questionAll[i].answerList[this.funZM(this.result[i][v])]);
                      // console.log(this.questionAll[i].answerList[this.funZM(this.result[i][v])].identification);
                    if (this.questionAll[i].answerList[this.funZM(this.result[i][v])].identification == '1') {
                        return Notify({ type: 'warning', message: '您有未完成的题目，请填写具体说明！' });
                     }
                    }
                }
            }
        }

        // let aboutResult = []
        // for(var j in this.result){
        //     if(typeof this.result[j] !== 'string'){
        //        aboutResult[j] = this.result[j].join('/')
        //     }else{
        //       aboutResult[j] = this.result[j]
        //     }
        // }

        // let data = {
        //     examAppCode:this.examAppCode,
        //     examCode:this.examCode,
        //     publishUsername:store.state.user.user.username,
        //     residueTime:this.stime,
        //     // examAnswer:this.result.toString(),
        //     // examAnswer:this.result,
        //     examAnswer:aboutResult.toString(),
        //     examRecord:this.examRecord,
        // }

        // let arrs = []
        // for(let u in this.questionAll){
        //   if(this.questionAll[u].questionType == 'more'){
        //      arrs.push({questionCode:this.questionAll[u].questionCode,maxChooseNum:this.questionAll[u].maxChooseNum,examAnswer:aboutResult[u]})
        //   }else{
        //     arrs.push({questionCode:this.questionAll[u].questionCode,examAnswer:aboutResult[u]})
        //   }
        // }
        // data.examInfoList = arrs
        // console.log(data,'123456');
        // console.log(this.result);

        Dialog.confirm({
        title: '温馨提示',
        message: '您已完成了所有题目，请确认是否进行提交',
        confirmButtonColor:'#1989fa'
        }).then(() => {

          for(var i in this.result){
            if (typeof this.result[i] == 'string') {
                if (this.zdytext[i]) {
                    if (this.questionAll[i].answerList[this.funZM(this.result[i])] && this.questionAll[i].answerList[this.funZM(this.result[i])].identification == '1') {
                        this.result[i] = this.result[i] + ':' + this.zdytext[i]
                    }
                } 
            }else if(typeof this.result[i] == 'object') {
                if (this.zdytext[i]) {
                    let index =  this.result[i].length-1
                    this.result[i][index] = this.result[i][index] + ':' + this.zdytext[i]
                } 
            }
        }

        let aboutResult = []
        for(var j in this.result){
            if(typeof this.result[j] !== 'string'){
               aboutResult[j] = this.result[j].join('/')
            }else{
              aboutResult[j] = this.result[j]
            }
        }

        let data = {
            examAppCode:this.examAppCode,
            examCode:this.examCode,
            publishUsername:store.state.user.user.username,
            residueTime:this.stime,
            // examAnswer:this.result.toString(),
            // examAnswer:this.result,
            examAnswer:aboutResult.toString(),
            examRecord:this.examRecord,
        }

        let arrs = []
        for(let u in this.questionAll){
          if(this.questionAll[u].questionType == 'more'){
             arrs.push({questionCode:this.questionAll[u].questionCode,maxChooseNum:this.questionAll[u].maxChooseNum,examAnswer:aboutResult[u]})
          }else{
            arrs.push({questionCode:this.questionAll[u].questionCode,examAnswer:aboutResult[u]})
          }
        }
        data.examInfoList = arrs
        // console.log(data,'123456');
        // console.log(this.result);

            submitExam(data).then(res=>{
            if(res.status==200){
                clearInterval(this.IntX)
                this.result = []
                this.zdytext = []
                this.djsdiv = false
                this.$router.push({ name:'success', })
            }
        }) }).catch(() => {});
    },

      // 返回
		  // goBack(){
			//   this.$router.push({path: "/detail"});
		  // },
  }
}
</script>

<style>
.maintext{ margin-left: .3rem; margin-right: .3rem; }
.van-radio{ margin-bottom: 0.05rem; align-items:flex-start }
.subject{ margin-bottom: 0.2rem; }
.djs{ position: fixed; top: 0.8rem; right: 0.3rem; background: #fff; display: flex; align-items: center; z-index: 2; }
.question{ line-height: 0.22rem; margin-bottom: 0.1rem; font-weight: 700; }
.custom-field { border: 1px solid #e8e8e8; border-radius: 5px; padding: 5px; }
.van-checkbox{ align-items: flex-start; margin-bottom: 3px; }

.textDec{ color: #333; font-size: 14px; line-height: 22px; margin-bottom: 3px; }
.textDec h3{ text-align: center; font-weight: bold; margin-bottom: 1px; }
.textDec p{ text-indent: 2em; }

  /* .giftBox{
    text-align: center;
    position: absolute;
    top: 40%; 
  } */

</style>
