<template>
  <div>
    <div class="nav bg-theme color-f text-c  xxx">
        <!-- <i class="iconfont iconfanhui font16  pl06" style="width:60px"  @click="goBack"><van-icon name="arrow-left" /></i> -->
        <span style="width:70px" @click="goBack">返回主页</span>
        <span class="font17 ml16">答题排行榜</span>
        <span style="width:70px" @click="myRank">我的排名</span>
    </div>

    <ul class="card">
      <li v-for="item in lists" :key="item.id">
        <!-- <span style="font-weight:700">排名：{{item.rank}}</span> -->
        <div class="flexsb" style="padding:5px 0;font-weight:700">
          <div>排名：{{item.rank}}</div>
          <div>总分：{{item.totalScore}}</div>
        </div>
        <div >
          <div  style="padding:5px 0;">{{item.ansewersTrueName}}（{{item.belongOrgName}}）</div>
          <div>累计答题用时：{{item.timeSpentMinutes.split('.')[0] || ''}}分{{item.timeSpentMinutes.split('.')[1] || '0'}}秒</div>
        </div>
      </li>
    </ul>

   
  </div>
</template>

<script>
import { Notify } from 'vant';
import { Dialog,Toast} from 'vant';
import { getSocreRanking } from "@/api/test.js";

export default {
    name: 'rank',
  
    data () {
        return {
          lists:[],
          self:{}
        }
    },
    created() {
      this.getList()
    },
     
    methods: {
      getList(){
        getSocreRanking().then(res=>{
          this.lists = res.data.page.content
          this.self = res.data.self
        })
      },
      goBack(){
          this.$router.back(-1);    //返回上一页  
      },
      myRank(){
          Dialog.alert({ title: '我的排名', message: `${this.self.ansewersTrueName}（${this.self.belongOrgName}）</br>排名：${this.self.rank == '9999999' ? '无' : this.self.rank}</br>总分：${this.self.totalScore}</br>累计用时：${this.self.timeSpentMinutes.split('.')[0]||'0'}分${this.self.timeSpentMinutes.split('.')[1] || '0'}秒`, }).then(() => {});
      }

       
    }
}
</script>

<style scoped>
    .content {
        position: absolute;
        top: 0.42rem;
        right: 0;
        left: 0;
        /* background: red; */
        overflow-y: auto;
    }

    .van-button__content {font-size:unset;}

     .nav{
        width: 100%;
        height: .4rem;
        line-height: .4rem;
        padding: 0 .16rem 0 .1rem;
        position: fixed;
        top: 0;
        z-index: 10;
    }

    .card{
      width: 100%;
      min-height: 20vh;
      text-align: start;
      border: 1px solid #fff;
      background: #fff;
      border-radius: 5px;
      padding:  0 10px;

       position: absolute;
        top: 0.42rem;
        right: 0;
        left: 0;
        /* background: red; */
        overflow-y: auto;
    }
    .card li{
      padding: 0 0 5px;
      border-bottom: 1px solid #ddd;
    }
    .flexsb{
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .xxx{
      display: flex;
      justify-content: space-between;
    }
</style>
