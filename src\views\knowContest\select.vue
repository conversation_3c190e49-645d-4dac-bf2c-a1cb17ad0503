<!-- 答题页 -->
<template>
    <div class="content">
       <div class="wordBox">
         <div class="tit">选择对战人</div>
      </div>
      <van-button icon="plus"  plain hairline type="info"  @click="toselectFun">点击选择对战人</van-button>
       <div class="wordBox">
         <div class="tits">邀请待办：</div>
      </div>
      <ul class="card">
        <li v-for="item in lists" :key="item.id"   @click="startFun(item)">
          <span style="font-weight:700">{{item.title}}</span>
          <div class="flexsb" style="padding:5px 0;">
            <div>被邀请人： {{ item.recTrueName}}</div>
            <div>邀请人： {{ item.sendTrueName}}</div>
          </div>
          <div class="flexsb">
            <div></div>
            <div>{{item.createdTime}}</div>
          </div>
        </li>
      </ul>


      <van-dialog v-model="showDialog" title="提示"   :show-cancel-button="false"  :show-confirm-button="false">
          <div style="padding:30px 0 40px">是否接受来自{{item.sendTrueName}}的对战邀请？</div>
          <button class="close-btn" @click="closeDialog">x</button>
          <!-- 自定义按钮 -->
          <div class="footer">
            <van-button type="default"  class="btnMy" @click="cancelAction">拒绝</van-button>
            <van-button type="info" class="btnMy" @click="confirmAction">接受</van-button>
          </div>
      </van-dialog>
    </div>
</template>
<script>
import store from '@/store';
import { Dialog,Toast} from 'vant';
import { InvitedTask,acceptInvitation,refauseInvitation,countExamWorkByTask, pendingList } from "@/api/test.js";
export default {
  name:'select',
  data() {
    return {
       user:store.state.user.user,//当前登陆人信息
       workType:'C',
       lists:[],
       showDialog:false,
       item:{}
    }
  },
  mounted() {},
  created(){
      this.getList()
  },
  activated(){},
  methods:{
    toselectFun(){
      this.workType = 'C'
      countExamWorkByTask({recUserName:''}).then(res=>{
        Dialog.confirm({ title: '温馨提示', message: `人人对战每人每天可参与${res.data.everyoneAnswerCount}次，剩余${res.data.unAnswerdCount}次!` }).then(() => {
            if(res.data.answerdCount == res.data.everyoneAnswerCount){
                Dialog.alert({ title: '温馨提示', message: '您今日人人对战已完成，请明日继续参与。', }).then(() => {});
            }else{
                // this.$router.push({ name:'select', params:{workType:this.workType}})
                this.$router.push({path:'/getOrgAndUser',query: {}});
            }
        }).catch(() => {});
      })
    },
    getList(){
      // InvitedTask().then(res=>{
      //   this.lists = res.data
      // })
      pendingList().then(res=>{
        this.lists = res.data.map(item => {
          item.invitationId = item.id
          return {
            ...item
          }
        })
      })
    },
    startFun(item){
      if(item.taskStatus == "PENDING" && item.sendUserName == this.user.username){
        Toast.fail('对方暂未接受邀请');
        return
      }

      if(item.taskStatus == "PENDING" && item.recUserName == this.user.username){
        this.item = item
        this.showDialog = true
      }else if(item.taskStatus == 'ACCEPTED'){
           this.item = item
           this.$router.push({ name:'test', params:{workType:this.workType,pmInsId:this.item.pmInsId,invitationId:this.item.invitationId,answerRecordId:this.item.answerRecordId,status:'0'}})
      }else if(item.taskStatus == 'ONGOING'){
           this.item = item
           this.$router.push({ name:'test', params:{workType:this.workType,pmInsId:this.item.pmInsId,invitationId:this.item.invitationId,answerRecordId:this.item.answerRecordId,status:'1'}})
      }else if(item.taskStatus == 'Expired' && item.recUserName == this.user.username){
           Toast.fail('工单已过期！');
      }else if(item.taskStatus == 'refuse' && item.recUserName == this.user.username){
           Toast.fail('挑战邀请被拒绝！');
      }
    },
    closeDialog() {
      this.showDialog = false;
    },
    confirmAction() {
      this.showDialog = false;
      this.acceptInvitation();
    },
    cancelAction() {
      this.showDialog = false;
      this.refuseInvitation();
    },
    acceptInvitation() {
      acceptInvitation({ invitationId: this.item.invitationId }).then(res => {
         this.$router.push({ name:'test', params:{workType:this.workType,pmInsId:this.item.pmInsId,invitationId:this.item.invitationId,answerRecordId:this.item.answerRecordId,status:'0'}})
      });
    },
    refuseInvitation() {
      refauseInvitation({ invitationId: this.item.invitationId }).then(res => {
        this.getList();
      });
    }
  }
}

</script>

<style scoped>
.content{
	width: 100vw;
	min-height: 100vh;
	background: url("@/assets/images/zsjs/a1.jpg") no-repeat center center;
	background-size:  100% 100%;
	text-align: center;
  padding: 0 12vw;
	padding-top: 27vh;
}
.flex{
   display: flex;
   flex-direction: column;
   align-items: center;
}
.wordBox .tit{
  font-size: .18rem;
  font-weight: 700;
  margin: .1rem;
  margin-bottom: .4rem;
}

.wordBox .tits{
  font-size: .16rem;
  font-weight: 400;
  margin: .5rem 0 .2rem;
  text-align: start;
}
.card{
  width: 100%;
  min-height: 20vh;
  text-align: start;
  border: 1px solid #fff;
  background: #fff;
  border-radius: 5px;
  padding:  0 10px;
}
.card li{
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
}
.flexsb{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btnMy{
  width: 50%;
}
.close-btn {
  background: transparent;
  border: none;
  font-size: 16px;
  cursor: pointer;
  position: absolute;
  top: 26px;
  right:20px
}
</style>